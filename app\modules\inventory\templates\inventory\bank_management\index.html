{% extends "base.html" %}

{% block title %}Gestion Bancaire{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
<script src="{{ url_for('inventory.static', filename='js/bank_management.js') }}"></script>
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête Gestion Bancaire -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-3">
                <h2 class="mb-0">
                    <i class="fas fa-university text-info"></i> 
                    Gestion Bancaire
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.add_bank_account') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-plus"></i> Nouveau Compte
                    </a>
                    <a href="{{ url_for('inventory.add_bank_operation') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-exchange-alt"></i> Nouvelle Opération
                    </a>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Statistiques bancaires -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card bg-primary">
                    <div class="stat-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.total_accounts }}</h3>
                        <p>Comptes bancaires</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success">
                    <div class="stat-icon">
                        <i class="fas fa-euro-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ "%.2f"|format(stats.total_balance) }} €</h3>
                        <p>Solde total</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-warning">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.overdrawn_accounts }}</h3>
                        <p>Comptes à découvert</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-info">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.recent_operations_count }}</h3>
                        <p>Opérations récentes</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liste des comptes bancaires -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Comptes Bancaires</h5>
                    </div>
                    <div class="card-body">
                        {% if bank_accounts %}
                            <div class="row">
                                {% for account in bank_accounts %}
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-{{ 'success' if account.balance >= 0 else 'danger' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="card-title">
                                                        {{ account.name }}
                                                        {% if account.is_default %}
                                                            <span class="badge bg-primary">Défaut</span>
                                                        {% endif %}
                                                    </h6>
                                                    <p class="card-text">
                                                        <strong>Solde:</strong> 
                                                        <span class="text-{{ 'success' if account.balance >= 0 else 'danger' }}">
                                                            {{ "%.2f"|format(account.balance) }} €
                                                        </span>
                                                    </p>
                                                    {% if account.account_number %}
                                                        <small class="text-muted">{{ account.account_number }}</small>
                                                    {% endif %}
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                            type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" 
                                                               href="{{ url_for('inventory.bank_account_details', account_id=account.id) }}">
                                                                <i class="fas fa-eye"></i> Voir détails
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" 
                                                               href="{{ url_for('inventory.edit_bank_account', account_id=account.id) }}">
                                                                <i class="fas fa-edit"></i> Modifier
                                                            </a>
                                                        </li>
                                                        {% if not account.is_default %}
                                                        <li>
                                                            <form method="POST" 
                                                                  action="{{ url_for('inventory.set_default_account', account_id=account.id) }}"
                                                                  style="display: inline;">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-star"></i> Définir par défaut
                                                                </button>
                                                            </form>
                                                        </li>
                                                        {% endif %}
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <form method="POST" 
                                                                  action="{{ url_for('inventory.deactivate_account', account_id=account.id) }}"
                                                                  style="display: inline;"
                                                                  onsubmit="return confirm('Êtes-vous sûr de vouloir désactiver ce compte ?')">
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-ban"></i> Désactiver
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            
                                            {% if account.is_overdrawn %}
                                                <div class="alert alert-warning alert-sm mt-2">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    Découvert de {{ "%.2f"|format(account.overdraft_amount) }} €
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-university fa-3x mb-3"></i>
                                <h5>Aucun compte bancaire</h5>
                                <p>Commencez par ajouter votre premier compte bancaire</p>
                                <a href="{{ url_for('inventory.add_bank_account') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Ajouter un compte
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-bolt"></i> Actions Rapides</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('inventory.bank_transfer') }}" class="btn btn-outline-primary">
                                <i class="fas fa-exchange-alt"></i> Virement entre comptes
                            </a>
                            <a href="{{ url_for('inventory.cash_to_bank_deposit') }}" class="btn btn-outline-success">
                                <i class="fas fa-money-bill-wave"></i> Dépôt depuis caisse
                            </a>
                            <a href="{{ url_for('inventory.bank_reconciliation') }}" class="btn btn-outline-info">
                                <i class="fas fa-balance-scale"></i> Réconciliation
                            </a>
                            <a href="#" class="btn btn-outline-warning">
                                <i class="fas fa-file-export"></i> Exporter relevés
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Alertes -->
                {% if stats.overdrawn_accounts > 0 %}
                <div class="card mt-3">
                    <div class="card-header bg-warning">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Alertes</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong>{{ stats.overdrawn_accounts }}</strong> compte(s) à découvert
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Opérations récentes -->
        {% if recent_operations %}
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> Opérations Récentes</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Compte</th>
                                        <th>Type</th>
                                        <th>Montant</th>
                                        <th>Description</th>
                                        <th>Solde après</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for operation in recent_operations %}
                                    <tr>
                                        <td>{{ operation.operation_date | datetime }}</td>
                                        <td>{{ operation.bank_account.name }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if operation.is_credit else 'danger' }}">
                                                {{ operation.type_display }}
                                            </span>
                                        </td>
                                        <td class="text-{{ 'success' if operation.is_credit else 'danger' }}">
                                            {{ "+" if operation.is_credit else "-" }}{{ "%.2f"|format(operation.absolute_amount) }} €
                                        </td>
                                        <td>{{ operation.description[:50] }}{% if operation.description|length > 50 %}...{% endif %}</td>
                                        <td>{{ "%.2f"|format(operation.balance_after) }} €</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                Voir toutes les opérations
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.border-left-danger {
    border-left: 4px solid #dc3545 !important;
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}
</style>
{% endblock %}
