{% extends "base.html" %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Historique des Mouvements de Caisse</h1>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Date début</label>
                        <input type="date" class="form-control" name="start_date"
                               value="{{ request.args.get('start_date', '') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Date fin</label>
                        <input type="date" class="form-control" name="end_date"
                               value="{{ request.args.get('end_date', '') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Type d'opération</label>
                        <select class="form-control" name="operation_type">
                            <option value="">Tous</option>
                            {% for type in operation_types %}
                            <option value="{{ type.value }}"
                                    {% if request.args.get('operation_type') == type.value %}selected{% endif %}>
                                {{ type.value|title }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Filtrer</button>
                    <a href="{{ url_for('cash_register.history') }}" class="btn btn-secondary ml-2">
                        Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Résumé -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>Résumé de la période</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <h5>Total Entrées</h5>
                    <p class="h4 text-success">{{ summary.total_in }}€</p>
                </div>
                <div class="col-md-3">
                    <h5>Total Sorties</h5>
                    <p class="h4 text-danger">{{ summary.total_out }}€</p>
                </div>
                <div class="col-md-3">
                    <h5>Balance</h5>
                    <p class="h4 {% if summary.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                        {{ summary.balance }}€
                    </p>
                </div>
                <div class="col-md-3">
                    <h5>Nombre d'opérations</h5>
                    <p class="h4">{{ summary.total_operations }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des opérations -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3>Détail des Opérations</h3>
            <div>
                <button class="btn btn-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button class="btn btn-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> Export PDF
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="operations-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Montant</th>
                            <th>Mode</th>
                            <th>Source</th>
                            <th>Note</th>
                            <th>Utilisateur</th>
                            <th style="color: darkorange;">Table</th>
                            <th style="color: darkorange;">Reçu</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for op in operations.items %}
                        <tr>
                            <td>{{ op.date | datetime }}</td>
                            <td>{{ op.type.value|title }}</td>
                            <td class="{% if op.amount >= 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ op.amount }}€
                            </td>
                            <td>{{ op.payment_method.value|title if op.payment_method else '-' }}</td>
                            <td>
                                {% if op.type == CashRegisterOperationType.SALE %}
                                Vente #{{ op.note.split('#')[1] if '#' in op.note else op.note }}
                                {% else %}
                                {{ op.source or '-' }}
                                {% endif %}
                            </td>
                            <td>{{ op.note }}</td>
                            <td>{{ op.user.username }}</td>
                            <td>
                                {% if op.table_number %}
                                    Table {{ op.table_number }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if op.receipt_path %}
                                <a href="{{ url_for('cash_register.download_receipt', operation_id=op.id) }}"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i>
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <!-- Pagination -->
                {% if operations.pages > 1 %}
                <nav aria-label="Navigation des pages" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if operations.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cash_register.history', page=operations.prev_num, start_date=start_date, end_date=end_date, operation_type=selected_type) }}">
                                Précédent
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in range(1, operations.pages + 1) %}
                        <li class="page-item {{ 'active' if page_num == operations.page else '' }}">
                            <a class="page-link" href="{{ url_for('cash_register.history', page=page_num, start_date=start_date, end_date=end_date, operation_type=selected_type) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% endfor %}

                        {% if operations.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cash_register.history', page=operations.next_num, start_date=start_date, end_date=end_date, operation_type=selected_type) }}">
                                Suivant
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>

<script>
function exportToExcel() {
    const table = document.getElementById('operations-table');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Opérations"});
    XLSX.writeFile(wb, `operations_caisse_${new Date().toISOString().split('T')[0]}.xlsx`);
}

function exportToPDF() {
    const doc = new jsPDF();
    doc.autoTable({ html: '#operations-table' });
    doc.save(`operations_caisse_${new Date().toISOString().split('T')[0]}.pdf`);
}
</script>
{% endblock %}
