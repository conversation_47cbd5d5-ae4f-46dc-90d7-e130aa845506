{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Pending Orders -->
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-utensils"></i>
                        Commandes en attente
                    </h6>
                    <button class="btn btn-sm btn-outline-primary" id="refresh-btn">
                        <i class="fas fa-sync-alt"></i> Actualiser
                    </button>
                </div>
                <div class="card-body">
                    <div class="row" id="orders-grid">
                        {% for order in orders %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-left-warning">
                                <div class="card-header bg-warning text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        Commande #{{ order.reference }}
                                        {% if order.table_number %}
                                        <span class="badge bg-light text-dark">Table {{ order.table_number }}</span>
                                        {% endif %}
                                    </h6>
                                    <small>{{ order.created_at | time }}</small>
                                </div>
                                <div class="card-body">
                                    <div class="order-items mb-3">
                                        <h6 class="text-muted mb-2">Articles:</h6>
                                        <ul class="list-unstyled">
                                            {% for item in order.items %}
                                            <li class="mb-2">
                                                <div class="d-flex justify-content-between">
                                                    <span>
                                                        <strong>{{ item.quantity }}x</strong>
                                                        {{ item.product.name }}
                                                    </span>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-success btn-sm item-ready" data-item-id="{{ item.id }}">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                {% if item.product.recipe %}
                                                <small class="text-muted d-block">
                                                    {{ item.product.recipe.description }}
                                                </small>
                                                {% endif %}
                                            </li>
                                            {% endfor %}
                                        </ul>
                                    </div>

                                    {% if order.kitchen_note %}
                                    <div class="order-notes">
                                        <h6 class="text-muted mb-2">Notes:</h6>
                                        <p class="mb-0 text-danger">{{ order.kitchen_note }}</p>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="card-footer bg-light">
                                    <div class="btn-group w-100">
                                        <button type="button" class="btn btn-success order-ready" data-order-id="{{ order.id }}">
                                            <i class="fas fa-check-circle"></i> Prêt
                                        </button>
                                        <button type="button" class="btn btn-info order-delivered" data-order-id="{{ order.id }}">
                                            <i class="fas fa-truck"></i> Livré
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="col-12">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i>
                                Aucune commande en attente pour le moment.
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-warning {
    border-left: .25rem solid #f6c23e!important;
}
.order-items {
    max-height: 300px;
    overflow-y: auto;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto refresh every minute
    setInterval(function() {
        location.reload();
    }, 60000);

    // Manual refresh
    document.getElementById('refresh-btn').addEventListener('click', function() {
        location.reload();
    });

    // Mark item as ready
    document.querySelectorAll('.item-ready').forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            // Add AJAX call to update item status
            this.classList.remove('btn-outline-success');
            this.classList.add('btn-success');
            this.disabled = true;
        });
    });

    // Mark order as ready
    document.querySelectorAll('.order-ready').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            // Add AJAX call to update order status
            const card = this.closest('.card');
            card.classList.remove('border-left-warning');
            card.classList.add('border-left-success');
            this.disabled = true;
        });
    });

    // Mark order as delivered
    document.querySelectorAll('.order-delivered').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            // Add AJAX call to update order status
            const col = this.closest('.col-md-4');
            col.remove();
        });
    });
});
</script>
{% endblock %} 