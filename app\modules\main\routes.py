from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.modules.pos.models_sale import Sale, SaleStatus
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.expenses.models_expense import Expense
from app.modules.auth.models import User
from app.modules.auth.forms import ProfileForm
from app.utils.helpers import get_date_range, format_currency
from datetime import datetime, timedelta
from sqlalchemy import func
from . import bp

@bp.route('/')
@bp.route('/index')
def index():
    if not current_user.is_authenticated:
        return render_template('main/index.html')
    return redirect(url_for('main.dashboard'))

@bp.route('/dashboard')
@login_required
def dashboard():
    # Récupérer tous les produits
    all_products = Product.query.filter_by(owner_id=current_user.id).all()
    
    # Filtrer les produits en rupture ou stock faible
    low_stock_products = [
        product for product in all_products
        if product.get_available_quantity() <= product.minimum_stock
        or product.get_available_quantity() <= 0
    ]

    # Récupérer les ingrédients en rupture ou stock faible
    low_stock_ingredients = Ingredient.query.filter(
        Ingredient.owner_id == current_user.id,
        db.or_(
            Ingredient.stock_quantity <= Ingredient.minimum_stock,
            Ingredient.stock_quantity <= 0
        )
    ).all()

    # Calculer les statistiques du jour
    today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)

    # Ventes POS du jour
    pos_sales_today = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.created_at >= today_start,
        Sale.created_at <= today_end,
        Sale.status == SaleStatus.PAID,
        Sale.service_type != 'online_order'  # Exclure les commandes en ligne
    ).all()

    # Commandes en ligne du jour
    from app.modules.online_ordering_sites.models import OnlineOrder, OnlineOrderStatus, OnlineOrderingSite
    online_orders_today = OnlineOrder.query.join(
        OnlineOrderingSite, OnlineOrder.site_id == OnlineOrderingSite.id
    ).filter(
        OnlineOrderingSite.owner_id == current_user.id,
        OnlineOrder.ordered_at >= today_start,
        OnlineOrder.ordered_at <= today_end,
        OnlineOrder.status.in_([OnlineOrderStatus.CONFIRMED, OnlineOrderStatus.PREPARING,
                               OnlineOrderStatus.READY, OnlineOrderStatus.DELIVERED,
                               OnlineOrderStatus.COMPLETED])
    ).all()

    # Calculer les totaux
    pos_sales_total = sum(sale.total for sale in pos_sales_today)
    online_sales_total = sum(order.total_amount for order in online_orders_today)
    total_sales = pos_sales_total + online_sales_total

    pos_orders_count = len(pos_sales_today)
    online_orders_count = len(online_orders_today)
    total_orders = pos_orders_count + online_orders_count

    print(f"Debug - POS Sales: {pos_orders_count} orders, {pos_sales_total}€")
    print(f"Debug - Online Orders: {online_orders_count} orders, {online_sales_total}€")
    print(f"Debug - Total: {total_orders} orders, {total_sales}€")

    # Calculer les dépenses du jour
    expenses_today = Expense.query.filter(
        Expense.owner_id == current_user.id,
        Expense.date >= today_start.date(),
        Expense.date <= today_end.date()
    ).all()
    today_expenses = sum(expense.amount for expense in expenses_today)

    # Calculer le résultat du jour
    daily_result = total_sales - today_expenses

    # Préparer les données pour le graphique des ventes horaires
    hourly_pos_sales = {i: 0 for i in range(24)}
    hourly_online_sales = {i: 0 for i in range(24)}
    hourly_total_sales = {i: 0 for i in range(24)}

    # Ventes POS par heure
    for sale in pos_sales_today:
        hour = sale.created_at.hour
        hourly_pos_sales[hour] += sale.total
        hourly_total_sales[hour] += sale.total

    # Commandes en ligne par heure
    for order in online_orders_today:
        hour = order.ordered_at.hour
        hourly_online_sales[hour] += order.total_amount
        hourly_total_sales[hour] += order.total_amount

    hourly_labels = [f"{i:02d}h" for i in range(24)]
    hourly_pos_data = [hourly_pos_sales[i] for i in range(24)]
    hourly_online_data = [hourly_online_sales[i] for i in range(24)]
    hourly_total_data = [hourly_total_sales[i] for i in range(24)]

    return render_template('main/dashboard.html',
                         low_stock_products=low_stock_products,
                         low_stock_ingredients=low_stock_ingredients,
                         # Totaux globaux
                         total_sales=format_currency(total_sales),
                         total_orders=total_orders,
                         # Détails POS
                         pos_sales_total=format_currency(pos_sales_total),
                         pos_orders_count=pos_orders_count,
                         # Détails en ligne
                         online_sales_total=format_currency(online_sales_total),
                         online_orders_count=online_orders_count,
                         # Finances
                         today_expenses=format_currency(today_expenses),
                         daily_result=format_currency(daily_result),
                         # Graphiques
                         hourly_labels=hourly_labels,
                         hourly_pos_data=hourly_pos_data,
                         hourly_online_data=hourly_online_data,
                         hourly_total_data=hourly_total_data)

@bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    form = ProfileForm(user=current_user, obj=current_user)
    if form.validate_on_submit():
        current_user.username = form.username.data
        current_user.email = form.email.data
        current_user.timezone = form.timezone.data
        if form.new_password.data:
            current_user.set_password(form.new_password.data)
        db.session.commit()
        flash('Profil mis à jour avec succès!', 'success')
        return redirect(url_for('main.profile'))
    return render_template('main/profile.html', form=form)

@bp.route('/delete-account', methods=['POST'])
@login_required
def delete_account():
    confirmation = request.form.get('confirmation')
    if confirmation != 'SUPPRIMER':
        flash('Veuillez taper "SUPPRIMER" pour confirmer la suppression de votre compte.', 'error')
        return redirect(url_for('main.profile'))
    
    # Store the user ID before logout
    user_id = current_user.id
    
    # Logout the user
    from flask_login import logout_user
    logout_user()
    
    # Delete the user and their data
    user = User.query.get(user_id)
    if user:
        db.session.delete(user)
        db.session.commit()
        flash('Votre compte a été supprimé avec succès.', 'success')
    
    return redirect(url_for('main.index'))

@bp.route('/faq')
def faq():
    return render_template('faq.html') 