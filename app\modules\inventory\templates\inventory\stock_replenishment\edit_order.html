{% extends "base.html" %}

{% block title %}Modifier Commande {{ order.reference }}{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-3">
                <h2 class="mb-0">
                    <i class="fas fa-edit text-warning"></i> 
                    Modifier Commande {{ order.reference }}
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-eye"></i> Voir détails
                    </a>
                    <a href="{{ url_for('inventory.pending_orders') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i> Modification de la commande
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="editOrderForm">
                            <!-- Informations de base -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label">Fournisseur</label>
                                    <select class="form-select" id="supplierId" name="supplier_id">
                                        <option value="0">Aucun fournisseur</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" 
                                                {% if order.supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Date de livraison prévue</label>
                                    <input type="date" class="form-control" id="expectedDeliveryDate" 
                                           value="{{ order.expected_delivery_date | date(\'%Y-%m-%d\') if order.expected_delivery_date else '' }}">
                                </div>
                            </div>

                            <!-- Articles -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>Articles de la commande</h6>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addOrderItem()">
                                        <i class="fas fa-plus"></i> Ajouter un article
                                    </button>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="orderItemsTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Article</th>
                                                <th>Quantité</th>
                                                <th>Prix unitaire</th>
                                                <th>Total</th>
                                                <th>Notes</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="orderItemsBody">
                                            {% for item in order.items %}
                                            <tr class="order-item-row">
                                                <td>
                                                    <select class="form-select item-select" name="item_id">
                                                        <option value="">Sélectionner un article</option>
                                                        <optgroup label="Produits">
                                                            {% for product in products %}
                                                            <option value="product_{{ product.id }}" 
                                                                    data-type="product" 
                                                                    data-name="{{ product.name }}"
                                                                    {% if item.product_id == product.id %}selected{% endif %}>
                                                                {{ product.name }}
                                                            </option>
                                                            {% endfor %}
                                                        </optgroup>
                                                        <optgroup label="Ingrédients">
                                                            {% for ingredient in ingredients %}
                                                            <option value="ingredient_{{ ingredient.id }}" 
                                                                    data-type="ingredient" 
                                                                    data-name="{{ ingredient.name }}"
                                                                    {% if item.ingredient_id == ingredient.id %}selected{% endif %}>
                                                                {{ ingredient.name }}
                                                            </option>
                                                            {% endfor %}
                                                        </optgroup>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control quantity-input" 
                                                           value="{{ item.quantity }}" min="0" step="0.01">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control price-input" 
                                                           value="{{ item.unit_price }}" min="0" step="0.01">
                                                </td>
                                                <td>
                                                    <span class="total-display">{{ "%.2f"|format(item.total_price) }} €</span>
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control notes-input" 
                                                           value="{{ item.notes or '' }}" placeholder="Notes...">
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            onclick="removeOrderItem(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Totaux -->
                            <div class="row mb-4">
                                <div class="col-md-8"></div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Sous-total:</span>
                                                <span id="subtotalDisplay">{{ "%.2f"|format(order.subtotal) }} €</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Remise:</span>
                                                <span id="discountDisplay">{{ "%.2f"|format(order.discount_amount) }} €</span>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between fw-bold">
                                                <span>Total:</span>
                                                <span id="totalDisplay">{{ "%.2f"|format(order.total_amount) }} €</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-4">
                                <label class="form-label">Notes</label>
                                <textarea class="form-control" id="orderNotes" rows="3" 
                                          placeholder="Notes sur la commande...">{{ order.notes or '' }}</textarea>
                            </div>

                            <!-- Actions -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Enregistrer les modifications
                                </button>
                                <a href="{{ url_for('inventory.purchase_order_details', order_id=order.id) }}" 
                                   class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Variables globales
let orderItems = [];
let products = {{ products|tojson }};
let ingredients = {{ ingredients|tojson }};

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    initializeOrderItems();
    attachEventListeners();
});

function initializeOrderItems() {
    // Charger les articles existants
    const rows = document.querySelectorAll('.order-item-row');
    rows.forEach(row => {
        updateRowCalculations(row);
    });
    updateTotals();
}

function attachEventListeners() {
    // Événements pour les calculs automatiques
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('quantity-input') || e.target.classList.contains('price-input')) {
            updateRowCalculations(e.target.closest('tr'));
            updateTotals();
        }
    });

    // Soumission du formulaire
    document.getElementById('editOrderForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveOrder();
    });
}

function addOrderItem() {
    const tbody = document.getElementById('orderItemsBody');
    const newRow = document.createElement('tr');
    newRow.className = 'order-item-row';
    
    newRow.innerHTML = `
        <td>
            <select class="form-select item-select" name="item_id">
                <option value="">Sélectionner un article</option>
                <optgroup label="Produits">
                    ${products.map(product => 
                        `<option value="product_${product.id}" data-type="product" data-name="${product.name}">${product.name}</option>`
                    ).join('')}
                </optgroup>
                <optgroup label="Ingrédients">
                    ${ingredients.map(ingredient => 
                        `<option value="ingredient_${ingredient.id}" data-type="ingredient" data-name="${ingredient.name}">${ingredient.name}</option>`
                    ).join('')}
                </optgroup>
            </select>
        </td>
        <td>
            <input type="number" class="form-control quantity-input" value="1" min="0" step="0.01">
        </td>
        <td>
            <input type="number" class="form-control price-input" value="0" min="0" step="0.01">
        </td>
        <td>
            <span class="total-display">0.00 €</span>
        </td>
        <td>
            <input type="text" class="form-control notes-input" placeholder="Notes...">
        </td>
        <td>
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeOrderItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    tbody.appendChild(newRow);
    updateTotals();
}

function removeOrderItem(button) {
    button.closest('tr').remove();
    updateTotals();
}

function updateRowCalculations(row) {
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.price-input').value) || 0;
    const total = quantity * price;
    
    row.querySelector('.total-display').textContent = total.toFixed(2) + ' €';
}

function updateTotals() {
    let subtotal = 0;
    
    document.querySelectorAll('.order-item-row').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(row.querySelector('.price-input').value) || 0;
        subtotal += quantity * price;
    });
    
    const discount = {{ order.discount_amount }};
    const total = subtotal - discount;
    
    document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2) + ' €';
    document.getElementById('totalDisplay').textContent = total.toFixed(2) + ' €';
}

function saveOrder() {
    const items = [];
    
    document.querySelectorAll('.order-item-row').forEach(row => {
        const itemSelect = row.querySelector('.item-select');
        const selectedOption = itemSelect.options[itemSelect.selectedIndex];
        
        if (selectedOption.value) {
            const [type, id] = selectedOption.value.split('_');
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const notes = row.querySelector('.notes-input').value;
            
            items.push({
                [type + '_id']: parseInt(id),
                item_name: selectedOption.dataset.name,
                quantity: quantity,
                unit_price: price,
                notes: notes
            });
        }
    });
    
    if (items.length === 0) {
        alert('Veuillez ajouter au moins un article à la commande');
        return;
    }
    
    const orderData = {
        supplier_id: parseInt(document.getElementById('supplierId').value) || 0,
        expected_delivery_date: document.getElementById('expectedDeliveryDate').value,
        notes: document.getElementById('orderNotes').value,
        items: items,
        subtotal: parseFloat(document.getElementById('subtotalDisplay').textContent.replace(' €', '')),
        total_amount: parseFloat(document.getElementById('totalDisplay').textContent.replace(' €', '')),
        discount: {
            amount: {{ order.discount_amount }},
            type: '{{ order.discount_type }}'
        }
    };
    
    fetch(`/inventory/stock-replenishment/orders/{{ order.id }}/edit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Commande modifiée avec succès');
            window.location.href = `/inventory/stock-replenishment/orders/{{ order.id }}`;
        } else {
            alert('Erreur: ' + (data.error || 'Erreur inconnue'));
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de connexion');
    });
}
</script>
{% endblock %}
