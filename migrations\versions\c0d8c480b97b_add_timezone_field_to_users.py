"""Add timezone field to users

Revision ID: c0d8c480b97b
Revises: 4d482525a7ad
Create Date: 2025-09-23 16:33:36.905966

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c0d8c480b97b'
down_revision = '4d482525a7ad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('customer_users', schema=None) as batch_op:
        batch_op.drop_column('timezone')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('customer_users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('timezone', sa.VARCHAR(length=50), server_default=sa.text('("UTC")'), nullable=True))

    # ### end Alembic commands ###
