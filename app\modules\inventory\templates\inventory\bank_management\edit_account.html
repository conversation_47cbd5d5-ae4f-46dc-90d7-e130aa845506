{% extends "base.html" %}

{% block title %}Modifier Compte Bancaire{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-edit"></i> Modifier Compte Bancaire
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.bank_account_details', account_id=account.id) }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-university"></i> Informations du Compte</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <!-- Nom du compte -->
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control") }}
                                {% if form.name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Numéro de compte -->
                            <div class="mb-3">
                                {{ form.account_number.label(class="form-label") }}
                                {{ form.account_number(class="form-control") }}
                                {% if form.account_number.errors %}
                                    <div class="text-danger">
                                        {% for error in form.account_number.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- IBAN -->
                            <div class="mb-3">
                                {{ form.iban.label(class="form-label") }}
                                {{ form.iban(class="form-control") }}
                                {% if form.iban.errors %}
                                    <div class="text-danger">
                                        {% for error in form.iban.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- BIC -->
                            <div class="mb-3">
                                {{ form.bic.label(class="form-label") }}
                                {{ form.bic(class="form-control") }}
                                {% if form.bic.errors %}
                                    <div class="text-danger">
                                        {% for error in form.bic.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Banque -->
                            <div class="mb-3">
                                {{ form.bank_name.label(class="form-label") }}
                                {{ form.bank_name(class="form-control") }}
                                {% if form.bank_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.bank_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Agence -->
                            <div class="mb-3">
                                {{ form.branch.label(class="form-label") }}
                                {{ form.branch(class="form-control") }}
                                {% if form.branch.errors %}
                                    <div class="text-danger">
                                        {% for error in form.branch.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Solde initial -->
                            <div class="mb-3">
                                {{ form.initial_balance.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.initial_balance(class="form-control", step="0.01") }}
                                    <span class="input-group-text">€</span>
                                </div>
                                {% if form.initial_balance.errors %}
                                    <div class="text-danger">
                                        {% for error in form.initial_balance.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Solde actuel: {{ "%.2f"|format(account.balance) }} €
                                </div>
                            </div>

                            <!-- Compte actif -->
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger">
                                        {% for error in form.is_active.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="3") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Boutons -->
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('inventory.bank_account_details', account_id=account.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Enregistrer les modifications
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Informations complémentaires -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> Informations</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>Modification du compte</h6>
                            <p class="small mb-2">
                                Vous pouvez modifier les informations de ce compte bancaire.
                            </p>
                            <ul class="small mb-0">
                                <li>Le nom et les coordonnées bancaires</li>
                                <li>Le statut actif/inactif</li>
                                <li>Les notes personnelles</li>
                            </ul>
                        </div>

                        <div class="mt-3">
                            <h6>Informations actuelles</h6>
                            <div class="border-bottom py-2">
                                <strong>Nom:</strong><br>
                                <span class="text-muted">{{ account.name }}</span>
                            </div>
                            <div class="border-bottom py-2">
                                <strong>Solde:</strong><br>
                                <span class="text-{{ 'success' if account.balance >= 0 else 'danger' }}">
                                    {{ "%.2f"|format(account.balance) }} €
                                </span>
                            </div>
                            <div class="border-bottom py-2">
                                <strong>Statut:</strong><br>
                                <span class="badge bg-{{ 'success' if account.is_active else 'secondary' }}">
                                    {{ 'Actif' if account.is_active else 'Inactif' }}
                                </span>
                            </div>
                            <div class="border-bottom py-2">
                                <strong>Créé le:</strong><br>
                                <span class="text-muted">{{ account.created_at | date }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions supplémentaires -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-cogs"></i> Actions</h6>
                    </div>
                    <div class="card-body">
                        <a href="{{ url_for('inventory.add_bank_operation') }}?account_id={{ account.id }}" class="btn btn-outline-primary btn-sm w-100 mb-2">
                            <i class="fas fa-plus"></i> Ajouter une opération
                        </a>
                        <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-outline-secondary btn-sm w-100 mb-2">
                            <i class="fas fa-list"></i> Tous les comptes
                        </a>
                        {% if account.is_active %}
                        <button class="btn btn-outline-warning btn-sm w-100" onclick="toggleAccountStatus({{ account.id }}, false)">
                            <i class="fas fa-pause"></i> Désactiver
                        </button>
                        {% else %}
                        <button class="btn btn-outline-success btn-sm w-100" onclick="toggleAccountStatus({{ account.id }}, true)">
                            <i class="fas fa-play"></i> Activer
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleAccountStatus(accountId, activate) {
    const action = activate ? 'activer' : 'désactiver';
    if (confirm(`Êtes-vous sûr de vouloir ${action} ce compte ?`)) {
        fetch(`/inventory/bank-management/accounts/${accountId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ is_active: activate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + (data.error || 'Erreur inconnue'));
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur de connexion');
        });
    }
}
</script>
{% endblock %}
