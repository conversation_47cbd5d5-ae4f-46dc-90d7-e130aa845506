{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">Détails du Client</h2>
                        <div>
                            <a href="{{ url_for('customers.edit', id=customer.id) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete({{ customer.id }})">
                                <i class="fas fa-trash"></i> Supprimer
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informations Personnelles</h5>
                            <table class="table">
                                <tr>
                                    <th>Nom complet</th>
                                    <td>{{ customer.first_name }} {{ customer.last_name }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>{{ customer.email or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Téléphone</th>
                                    <td>{{ customer.phone or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Adresse</th>
                                    <td>{{ customer.address or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Programme de Fidélité</h5>
                            <table class="table">
                                <tr>
                                    <th>Points actuels</th>
                                    <td>{{ customer.loyalty_points }}</td>
                                </tr>
                                <tr>
                                    <th>Date d'inscription</th>
                                    <td>{{ customer.created_at | date }}</td>
                                </tr>
                                <tr>
                                    <th>Dernière visite</th>
                                    <td>
                                        {% if customer.last_visit %}
                                            {{ customer.last_visit | date }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <h5>Historique des Achats</h5>
                            <form method="get" class="mb-2 d-flex align-items-center">
                                <label for="per_page" class="me-2 mb-0">Afficher</label>
                                <select name="per_page" id="per_page" class="form-select form-select-sm w-auto me-2" onchange="this.form.submit()">
                                    <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                                </select>
                                <span class="me-2">par page</span>
                                <input type="hidden" name="page" value="{{ page }}">
                            </form>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Points gagnés</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sale in customer.sales %}
                                    <tr>
                                        <td>{{ sale.created_at | datetime }}</td>
                                        <td>{{ "%.2f"|format(sale.total) }} €</td>
                                        <td>{{ sale.loyalty_points_earned or 0 }}</td>
                                        <td>
                                            <a href="{{ url_for('pos.sale_details', id=sale.id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Voir
                                            </a>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center">Aucun achat enregistré</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                            {% if total_pages > 1 %}
                            <nav aria-label="Page navigation" class="mt-2">
                                <ul class="pagination justify-content-center">
                                    {% for p in range(1, total_pages + 1) %}
                                    <li class="page-item {% if p == page %}active{% endif %}">
                                        <a class="page-link" href="?page={{ p }}&per_page={{ per_page }}">{{ p }}</a>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </nav>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('customers.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Widget des actions rapides -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Actions Rapides</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('pos.index', customer_id=customer.id) }}" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Nouvelle Vente
                        </a>
                        <button class="btn btn-success" onclick="adjustPoints()">
                            <i class="fas fa-star"></i> Ajuster Points
                        </button>
                        <button class="btn btn-info" onclick="sendEmail()">
                            <i class="fas fa-envelope"></i> Envoyer Email
                        </button>
                    </div>
                </div>
            </div>

            <!-- Widget des statistiques -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Statistiques Client</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total des achats
                            <span class="badge bg-primary rounded-pill">
                                {{ "%.2f"|format(customer.sales|sum(attribute='total')) }} €
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Nombre d'achats
                            <span class="badge bg-primary rounded-pill">
                                {{ customer.sales|length }}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Panier moyen
                            <span class="badge bg-primary rounded-pill">
                                {% if customer.sales|length > 0 %}
                                    {{ "%.2f"|format(customer.sales|sum(attribute='total') / customer.sales|length) }} €
                                {% else %}
                                    0.00 €
                                {% endif %}
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer ce client ? Cette action est irréversible.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'ajustement des points -->
<div class="modal fade" id="pointsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajuster les Points de Fidélité</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="pointsForm">
                    <div class="mb-3">
                        <label class="form-label">Points actuels: {{ customer.loyalty_points }}</label>
                    </div>
                    <div class="mb-3">
                        <label for="points" class="form-label">Ajustement</label>
                        <input type="number" class="form-control" id="points" name="points" value="0">
                        <small class="form-text text-muted">
                            Utilisez des valeurs positives pour ajouter des points, négatives pour en retirer
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="savePoints()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(customerId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = `/customers/${customerId}/delete`;
    modal.show();
}

function adjustPoints() {
    const modal = new bootstrap.Modal(document.getElementById('pointsModal'));
    modal.show();
}

function savePoints() {
    const points = document.getElementById('points').value;
    fetch(`/customers/{{ customer.id }}/adjust-points`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ points: parseInt(points) })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erreur lors de l\'ajustement des points');
        }
    });
}

function sendEmail() {
    // Implémenter l'envoi d'email
    alert('Fonctionnalité à venir');
}
</script>
{% endblock %} 