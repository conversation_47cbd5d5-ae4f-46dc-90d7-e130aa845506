from datetime import datetime, timedelta, date
from calendar import monthrange
import pytz
from flask import current_app, request
from flask_login import current_user

def get_user_timezone():
    """Récupère le fuseau horaire de l'utilisateur connecté (User ou CustomerUser)"""
    from flask import session

    # Vérifier d'abord si c'est un CustomerUser (commandes en ligne)
    customer_id = session.get('customer_id')
    if customer_id:
        try:
            from app.modules.online_ordering_sites.models import CustomerUser
            customer = CustomerUser.query.get(customer_id)
            if customer and customer.timezone:
                return customer.timezone
        except:
            pass

    # Ensuite vérifier current_user (utilisateurs POS)
    if current_user.is_authenticated and hasattr(current_user, 'timezone') and current_user.timezone:
        return current_user.timezone

    # Fallback vers les paramètres système
    try:
        from app.modules.settings.models_settings import Settings
        if current_user.is_authenticated:
            settings = Settings.query.filter_by(owner_id=current_user.id).first()
            if settings and settings.timezone:
                return settings.timezone
    except:
        pass

    return current_app.config.get('TIMEZONE', 'Europe/Paris')

def get_customer_timezone(customer_id=None):
    """Récupère le fuseau horaire d'un CustomerUser spécifique"""
    from flask import session

    if not customer_id:
        customer_id = session.get('customer_id')

    if customer_id:
        try:
            from app.modules.online_ordering_sites.models import CustomerUser
            customer = CustomerUser.query.get(customer_id)
            if customer and customer.timezone:
                return customer.timezone
        except:
            pass

    return 'Europe/Paris'

def convert_utc_to_user_timezone(utc_datetime, user_timezone=None):
    """Convertit une date UTC vers le fuseau horaire de l'utilisateur"""
    if utc_datetime is None:
        return None

    if user_timezone is None:
        user_timezone = get_user_timezone()

    # S'assurer que la date est en UTC
    if utc_datetime.tzinfo is None:
        utc_datetime = pytz.utc.localize(utc_datetime)
    elif utc_datetime.tzinfo != pytz.utc:
        utc_datetime = utc_datetime.astimezone(pytz.utc)

    # Convertir vers le fuseau horaire utilisateur
    user_tz = pytz.timezone(user_timezone)
    return utc_datetime.astimezone(user_tz)

def get_current_time(timezone=None):
    if timezone is None:
        timezone = get_user_timezone()
    tz = pytz.timezone(timezone)
    return datetime.now(tz)

def format_date(date_obj, format_str=None, user_timezone=None):
    if date_obj is None:
        return ''

    # Convertir vers le fuseau horaire utilisateur si c'est une date UTC
    if hasattr(date_obj, 'tzinfo') and date_obj.tzinfo is not None:
        date_obj = convert_utc_to_user_timezone(date_obj, user_timezone)

    if format_str is None:
        format_str = current_app.config.get('DATE_FORMAT', '%d/%m/%Y')
    return date_obj.strftime(format_str)

def format_time(time_obj, format_str=None, user_timezone=None):
    if time_obj is None:
        return ''

    # Convertir vers le fuseau horaire utilisateur si c'est une date UTC
    if hasattr(time_obj, 'tzinfo') and time_obj.tzinfo is not None:
        time_obj = convert_utc_to_user_timezone(time_obj, user_timezone)

    if format_str is None:
        format_str = current_app.config.get('TIME_FORMAT', '%H:%M')
    return time_obj.strftime(format_str)

def format_datetime(datetime_obj, date_format=None, time_format=None, user_timezone=None):
    if datetime_obj is None:
        return ''

    # Convertir vers le fuseau horaire utilisateur
    converted_datetime = convert_utc_to_user_timezone(datetime_obj, user_timezone)
    if converted_datetime is None:
        return ''

    date_str = format_date(converted_datetime, date_format, user_timezone)
    time_str = format_time(converted_datetime, time_format, user_timezone)
    return f"{date_str} {time_str}"

def format_datetime_for_export(datetime_obj, format_str='%d/%m/%Y', user_timezone=None):
    """Formate une date pour les exports en tenant compte du fuseau horaire utilisateur"""
    if datetime_obj is None:
        return ''

    # Convertir vers le fuseau horaire utilisateur
    converted_datetime = convert_utc_to_user_timezone(datetime_obj, user_timezone)
    if converted_datetime is None:
        return ''

    return converted_datetime.strftime(format_str)

def format_datetime_with_timezone_for_export(datetime_obj, format_str='%d/%m/%Y %H:%M', user_timezone=None):
    """Formate une date avec heure pour les exports en tenant compte du fuseau horaire utilisateur"""
    if datetime_obj is None:
        return ''

    # Convertir vers le fuseau horaire utilisateur
    converted_datetime = convert_utc_to_user_timezone(datetime_obj, user_timezone)
    if converted_datetime is None:
        return ''

    return converted_datetime.strftime(format_str)

def get_date_range(range_type):
    today = date.today()
    
    if range_type == 'today':
        return today, today
    
    elif range_type == 'yesterday':
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday
    
    elif range_type == 'this_week':
        start = today - timedelta(days=today.weekday())
        end = start + timedelta(days=6)
        return start, end
    
    elif range_type == 'this_month':
        start = today.replace(day=1)
        end = today.replace(day=monthrange(today.year, today.month)[1])
        return start, end
    
    elif range_type == 'this_year':
        start = today.replace(month=1, day=1)
        end = today.replace(month=12, day=31)
        return start, end
    
    return None, None

def format_currency(amount, currency=None):
    if currency is None:
        currency = current_app.config.get('CURRENCY', 'EUR')
    
    if currency == 'EUR':
        return f"{amount:.2f} €"
    elif currency == 'USD':
        return f"${amount:.2f}"
    elif currency == 'GBP':
        return f"£{amount:.2f}"
    
    return f"{amount:.2f} {currency}"

def calculate_percentage(part, whole):
    if whole == 0:
        return 0
    return (part / whole) * 100

def generate_reference(prefix, number):
    return f"{prefix}{number:06d}"

def format_phone_number(phone):
    if not phone:
        return ""
    
    # Supprimer tous les caractères non numériques
    cleaned = ''.join(filter(str.isdigit, phone))
    
    # Format français
    if len(cleaned) == 10 and cleaned.startswith('0'):
        return f"{cleaned[:2]} {cleaned[2:4]} {cleaned[4:6]} {cleaned[6:8]} {cleaned[8:]}"

    return phone

def is_valid_email(email):
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def truncate_string(string, length=50, suffix='...'):
    if len(string) <= length:
        return string
    return string[:length].rsplit(' ', 1)[0] + suffix

def get_date_range(period):
    today = datetime.utcnow()
    if period == 'today':
        start_date = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'week':
        start_date = (today - timedelta(days=today.weekday())).replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'month':
        start_date = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'quarter':
        quarter = (today.month - 1) // 3 + 1
        start_date = datetime(today.year, 3 * quarter - 2, 1)
        end_date = datetime(today.year, 3 * quarter + 1, 1) - timedelta(days=1)
    elif period == 'semester':
        semester = 1 if today.month <= 6 else 2
        start_date = datetime(today.year, 1 if semester == 1 else 7, 1)
        end_date = datetime(today.year, 6 if semester == 1 else 12, 30)
    elif period == 'year':
        start_date = today.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(month=12, day=31, hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'custom':
        start_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
        end_date = datetime.strptime(request.args.get('end_date'), '%Y-%m-%d')
        end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif period == 'specific':
        specific_date = datetime.strptime(request.args.get('start_date'), '%Y-%m-%d')
        start_date = specific_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = specific_date.replace(hour=23, minute=59, second=59, microsecond=999999)
    else:
        start_date = today.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    return start_date, end_date