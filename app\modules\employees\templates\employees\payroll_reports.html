{% extends "employees/base_hr.html" %}

{% block title %}Rapports de Paie{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-bar me-2"></i>Rapports de Paie
    </h1>
    <div>
        <button class="btn btn-outline-success me-2" onclick="exportPayrollData()">
            <i class="fas fa-download me-1"></i>Exporter
        </button>
        <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

<!-- Statistiques du mois -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.2f"|format(total_gross_pay or 0) }}€</h4>
                        <p class="mb-0">Salaire Brut Total</p>
                    </div>
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.2f"|format(total_net_pay or 0) }}€</h4>
                        <p class="mb-0">Salaire Net Total</p>
                    </div>
                    <i class="fas fa-hand-holding-usd fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.2f"|format(total_deductions or 0) }}€</h4>
                        <p class="mb-0">Déductions Totales</p>
                    </div>
                    <i class="fas fa-minus-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ monthly_payrolls|length }}</h4>
                        <p class="mb-0">Fiches de Paie</p>
                    </div>
                    <i class="fas fa-file-invoice-dollar fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="month" class="form-label">Mois</label>
                <select class="form-select" id="month" name="month">
                    {% for i in range(1, 13) %}
                        <option value="{{ i }}" {{ 'selected' if i == current_month.month }}>
                            {{ ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
                                 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'][i] }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="year" class="form-label">Année</label>
                <select class="form-select" id="year" name="year">
                    {% for year in range(2020, 2030) %}
                        <option value="{{ year }}" {{ 'selected' if year == current_month.year }}>{{ year }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-filter me-1"></i>Filtrer
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Liste des fiches de paie -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            Fiches de Paie - {{ current_month | date('%B %Y')|title }}
        </h5>
    </div>
    <div class="card-body p-0">
        {% if monthly_payrolls %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Employé</th>
                        <th>Période</th>
                        <th>Heures Normales</th>
                        <th>Heures Sup.</th>
                        <th>Salaire Brut</th>
                        <th>Déductions</th>
                        <th>Salaire Net</th>
                        <th>Statut</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payroll in monthly_payrolls %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ payroll.employee.first_name[0] }}{{ payroll.employee.last_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ payroll.employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ payroll.employee.position }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            {{ payroll.pay_period_start | date('%d/%m') }} - {{ payroll.pay_period_end | date }}
                        </td>
                        <td>{{ "%.1f"|format(payroll.regular_hours or 0) }}h</td>
                        <td>{{ "%.1f"|format(payroll.overtime_hours or 0) }}h</td>
                        <td><strong>{{ "%.2f"|format(payroll.gross_pay or 0) }}€</strong></td>
                        <td>{{ "%.2f"|format(payroll.total_deductions or 0) }}€</td>
                        <td><strong class="text-success">{{ "%.2f"|format(payroll.net_pay or 0) }}€</strong></td>
                        <td>
                            {% if payroll.is_processed %}
                                <span class="badge bg-success">Traitée</span>
                            {% else %}
                                <span class="badge bg-warning">En attente</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.edit_payroll', payroll_id=payroll.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        onclick="downloadPayslip({{ payroll.id }})" title="Télécharger">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune fiche de paie pour cette période</h5>
            <p class="text-muted">Les fiches de paie apparaîtront ici une fois créées.</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>

<script>
function exportPayrollData() {
    const month = document.getElementById('month').value;
    const year = document.getElementById('year').value;
    const url = `/employees/payroll/export?month=${month}&year=${year}`;
    window.open(url, '_blank');
}

function downloadPayslip(payrollId) {
    const url = `/employees/payroll/${payrollId}/download`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
