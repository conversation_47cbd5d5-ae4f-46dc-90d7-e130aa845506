/* Styles pour les pages des fournisseurs */

/* Styles pour les cartes de factures */
.invoice-card {
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.invoice-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007bff !important;
}

.invoice-card:hover::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 2px;
}

/* Styles pour les tooltips des factures */
.tooltip {
    font-size: 0.9rem;
    z-index: 1060;
}

.tooltip-inner {
    max-width: 400px;
    min-width: 300px;
    padding: 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: left;
}

/* Header du tooltip */
.invoice-tooltip .tooltip-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 600;
    font-size: 1rem;
}

.invoice-tooltip .tooltip-header .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Contenu du tooltip */
.invoice-tooltip .tooltip-content {
    padding: 16px;
}

.invoice-tooltip .tooltip-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.invoice-tooltip .tooltip-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.invoice-tooltip .tooltip-row i {
    width: 20px;
    margin-right: 12px;
    text-align: center;
    font-size: 0.9rem;
}

.invoice-tooltip .tooltip-row span {
    color: #ecf0f1;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Footer du tooltip */
.invoice-tooltip .tooltip-footer {
    background: rgba(0, 0, 0, 0.2);
    padding: 10px 16px;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.invoice-tooltip .tooltip-footer small {
    color: #bdc3c7;
    font-size: 0.75rem;
    font-style: italic;
}

/* Couleurs des icônes dans les tooltips */
.invoice-tooltip .text-info { color: #3498db !important; }
.invoice-tooltip .text-warning { color: #f39c12 !important; }
.invoice-tooltip .text-danger { color: #e74c3c !important; }
.invoice-tooltip .text-success { color: #27ae60 !important; }
.invoice-tooltip .text-primary { color: #3498db !important; }
.invoice-tooltip .text-secondary { color: #95a5a6 !important; }

/* Flèches des tooltips */
.tooltip.bs-tooltip-left .tooltip-arrow::before {
    border-left-color: #2c3e50;
}

.tooltip.bs-tooltip-right .tooltip-arrow::before {
    border-right-color: #2c3e50;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #2c3e50;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: #2c3e50;
}

/* Amélioration des cartes de factures */
.invoice-card h6 {
    color: #2c3e50;
    font-weight: 600;
}

.invoice-card .text-muted {
    color: #7f8c8d !important;
}

.invoice-card strong {
    color: #34495e;
}

/* Animation pour les badges */
.invoice-card .badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Responsive */
@media (max-width: 768px) {
    .tooltip-inner {
        max-width: 280px;
        min-width: 250px;
    }
    
    .invoice-tooltip .tooltip-header {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
    
    .invoice-tooltip .tooltip-content {
        padding: 12px;
    }
    
    .invoice-tooltip .tooltip-row {
        margin-bottom: 8px;
        padding: 4px 0;
    }
    
    .invoice-tooltip .tooltip-row i {
        width: 16px;
        margin-right: 8px;
        font-size: 0.8rem;
    }
    
    .invoice-tooltip .tooltip-row span {
        font-size: 0.8rem;
    }
    
    .invoice-tooltip .tooltip-footer {
        padding: 8px 12px;
    }
}

/* Effet de focus pour l'accessibilité */
.invoice-card:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Amélioration des boutons dans les cartes */
.invoice-card .btn {
    transition: all 0.2s ease;
}

.invoice-card .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
