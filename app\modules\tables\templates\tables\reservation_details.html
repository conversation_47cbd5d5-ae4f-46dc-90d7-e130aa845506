{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">Détails de la Réservation</h2>
                        <div>
                            <a href="{{ url_for('tables.edit_reservation', id=reservation.id) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <a href="{{ url_for('tables.reservations') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Informations Client</h5>
                            <table class="table">
                                <tr>
                                    <th>Nom</th>
                                    <td>{{ reservation.customer_name }}</td>
                                </tr>
                                <tr>
                                    <th>Téléphone</th>
                                    <td>{{ reservation.customer_phone }}</td>
                                </tr>
                                <tr>
                                    <th>Nombre de personnes</th>
                                    <td>{{ reservation.number_of_guests }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Informations Réservation</h5>
                            <table class="table">
                                <tr>
                                    <th>Table</th>
                                    <td>{{ reservation.table.display_name }}</td>
                                </tr>
                                <tr>
                                    <th>Date et heure</th>
                                    <td>{{ reservation.reservation_date | datetime(\'%d/%m/%Y\', \'à %H:%M\') }}</td>
                                </tr>
                                <tr>
                                    <th>Durée</th>
                                    <td>{{ reservation.duration_minutes }} minutes</td>
                                </tr>
                                <tr>
                                    <th>Statut</th>
                                    <td>
                                        {% if reservation.is_active() %}
                                            <span class="badge bg-success">En cours</span>
                                        {% elif reservation.reservation_date > now %}
                                            <span class="badge bg-warning">À venir</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Terminée</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if reservation.notes %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Notes</h5>
                            <div class="alert alert-info">
                                {{ reservation.notes }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Informations Système</h5>
                            <table class="table table-sm">
                                <tr>
                                    <th>Créée le</th>
                                    <td>{{ reservation.created_at | datetime(\'%d/%m/%Y\', \'à %H:%M\') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('tables.reservations') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour aux réservations
                        </a>
                        <div>
                            <a href="{{ url_for('tables.edit_reservation', id=reservation.id) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            {% if reservation.reservation_date > now %}
                                <button type="button" class="btn btn-danger" onclick="confirmCancel({{ reservation.id }})">
                                    <i class="fas fa-times"></i> Annuler
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation d'annulation -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer l'annulation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir annuler cette réservation ?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Non</button>
                <form id="cancelForm" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Oui, annuler</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmCancel(reservationId) {
    const modal = new bootstrap.Modal(document.getElementById('cancelModal'));
    const form = document.getElementById('cancelForm');
    form.action = `/tables/reservations/${reservationId}/cancel`;
    modal.show();
}
</script>
{% endblock %}
