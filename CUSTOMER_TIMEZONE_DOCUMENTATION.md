# 🌍 Gestion des Fuseaux Horaires pour CustomerUser

## Vue d'ensemble

Ce document décrit l'implémentation de la gestion des fuseaux horaires pour les clients (`CustomerUser`) du module de commandes en ligne. Cette fonctionnalité permet aux clients internationaux de voir les dates et heures dans leur fuseau horaire local.

## 🎯 Objectifs

- **Stockage UTC** : Toutes les dates restent stockées en UTC dans la base de données
- **Affichage localisé** : Chaque client voit les dates dans son fuseau horaire
- **Cohérence** : Même logique que pour les utilisateurs du POS (`User`)
- **Flexibilité** : Support de 40+ fuseaux horaires mondiaux

## 🔧 Implémentation

### 1. Modèle CustomerUser

**Fichier :** `app/modules/online_ordering_sites/models.py`

```python
class CustomerUser(UserMixin, db.Model):
    # ... autres champs ...
    timezone = db.Column(db.String(50), default='Europe/Paris')
```

**Migration :** Ajout du champ `timezone` avec valeur par défaut `Europe/Paris`

### 2. Formulaires

**Fichier :** `app/modules/online_ordering_sites/forms.py`

- `CustomerRegistrationForm` : Inclut le choix du fuseau horaire à l'inscription
- `CustomerProfileForm` : Permet de modifier le fuseau horaire dans le profil

**Fuseaux horaires supportés :**
- **Europe** : Paris, London, Berlin, Rome, Madrid, Amsterdam, etc.
- **Amérique** : New_York, Chicago, Los_Angeles, Toronto, Mexico_City, etc.
- **Asie** : Tokyo, Shanghai, Hong_Kong, Singapore, Dubai, etc.
- **Océanie** : Sydney, Melbourne, Perth
- **Afrique** : Cairo, Lagos, Johannesburg, Casablanca

### 3. Routes

**Fichier :** `app/modules/online_ordering_sites/routes.py`

```python
@bp.route('/customer/profile', methods=['GET', 'POST'])
def customer_profile():
    # Gestion du formulaire de profil avec timezone
    if form.validate_on_submit():
        customer.timezone = form.timezone.data
        db.session.commit()
```

### 4. Templates

**Fichier :** `app/templates/online_ordering_sites/customer_profile.html`

- Modal d'édition du profil avec sélection du fuseau horaire
- Affichage des dates avec les filtres timezone-aware
- Interface utilisateur intuitive

## 🎨 Interface Utilisateur

### Page de Profil Client

1. **Bouton "Modifier mon profil"** : Ouvre un modal d'édition
2. **Sélecteur de fuseau horaire** : Liste déroulante organisée par région
3. **Aide contextuelle** : Explication de l'impact du fuseau horaire
4. **Sauvegarde** : Mise à jour immédiate et redirection

### Affichage des Dates

Toutes les dates dans les templates clients utilisent automatiquement le fuseau horaire du client :

```html
<!-- Avant -->
{{ order.ordered_at.strftime('%d/%m/%Y à %H:%M') }}

<!-- Après -->
{{ order.ordered_at | datetime('%d/%m/%Y', 'à %H:%M') }}
```

## 🔄 Logique de Conversion

### Utilitaires Existants

Les fonctions dans `app/utils/helpers.py` fonctionnent automatiquement avec `CustomerUser` :

- `get_user_timezone()` : Détecte le type d'utilisateur (User ou CustomerUser)
- `convert_utc_to_user_timezone()` : Conversion UTC vers fuseau local
- `format_datetime_for_export()` : Formatage pour exports

### Filtres de Template

Les filtres dans `app/utils/filters.py` supportent les deux types d'utilisateurs :

- `{{ date | date }}` : Affichage de date
- `{{ datetime | datetime }}` : Affichage de date et heure
- `{{ time | time }}` : Affichage d'heure seule

## 📊 Cas d'Usage

### Scénario 1 : Client Français
- **Fuseau horaire** : `Europe/Paris` (UTC+1/+2)
- **Commande passée** : 14:30 UTC
- **Affichage client** : 15:30 (hiver) ou 16:30 (été)

### Scénario 2 : Client Américain
- **Fuseau horaire** : `America/New_York` (UTC-5/-4)
- **Commande passée** : 14:30 UTC
- **Affichage client** : 09:30 (hiver) ou 10:30 (été)

### Scénario 3 : Client Japonais
- **Fuseau horaire** : `Asia/Tokyo` (UTC+9)
- **Commande passée** : 14:30 UTC
- **Affichage client** : 23:30

## 🔧 Maintenance

### Mise à Jour des Clients Existants

Les clients existants reçoivent automatiquement le fuseau horaire par défaut `Europe/Paris`. Pour une mise à jour intelligente basée sur le pays :

```python
# Via Flask shell
from app.modules.online_ordering_sites.models import CustomerUser

customers = CustomerUser.query.filter(
    (CustomerUser.timezone == None) | (CustomerUser.timezone == '')
).all()

for customer in customers:
    # Logique basée sur customer.country si disponible
    customer.timezone = 'Europe/Paris'  # ou autre selon le pays

db.session.commit()
```

### Ajout de Nouveaux Fuseaux Horaires

Pour ajouter un nouveau fuseau horaire :

1. Modifier `CustomerRegistrationForm` dans `forms.py`
2. Modifier `CustomerProfileForm` dans `forms.py`
3. Tester la conversion avec `pytz.timezone('Nouveau/Fuseau')`

## ✅ Tests

### Vérification Manuelle

1. **Inscription** : Créer un compte avec un fuseau horaire spécifique
2. **Profil** : Modifier le fuseau horaire via le profil
3. **Commandes** : Vérifier l'affichage des dates de commande
4. **Historique** : Contrôler les dates dans l'historique

### Points de Contrôle

- [ ] Inscription avec fuseau horaire personnalisé
- [ ] Modification du fuseau horaire dans le profil
- [ ] Affichage correct des dates de commande
- [ ] Cohérence dans tous les templates
- [ ] Exports avec bon fuseau horaire

## 🚀 Avantages

1. **Expérience utilisateur** : Dates familières pour chaque client
2. **International** : Support des clients du monde entier
3. **Cohérence** : Même logique que le système POS principal
4. **Maintenance** : Code réutilisable et extensible
5. **Performance** : Conversion à l'affichage, pas de modification en base

## 📝 Notes Techniques

- **Stockage** : UTC uniquement en base de données
- **Conversion** : À l'affichage via les filtres de template
- **Fallback** : `Europe/Paris` par défaut
- **Compatibilité** : Fonctionne avec les utilitaires existants
- **Sécurité** : Validation des fuseaux horaires supportés
