{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">
        <i class="fas fa-chart-bar"></i> {{ title }} (POS)
    </h2>

    <!-- Statistiques générales -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6 class="card-title">Nombre de ventes (POS)</h6>
                    <h2 class="mb-0">{{ total_sales }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6 class="card-title">Chiffre d'affaires</h6>
                    <h2 class="mb-0">{{ "%.2f"|format(total_revenue) }} €</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h6 class="card-title">Panier moyen</h6>
                    <h2 class="mb-0">{{ "%.2f"|format(average_sale) }} €</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des ventes -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Ventes du jour</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Heure</th>
                                    <th>Référence</th>
                                    <th>Client</th>
                                    <th>Table</th>
                                    <th class="text-end">Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in sales %}
                                <tr>
                                    <td>{{ sale.created_at | time }}</td>
                                    <td>{{ sale.reference }}</td>
                                    <td>
                                        {% if sale.customer %}
                                        {{ sale.customer.first_name }} {{ sale.customer.last_name }}
                                        {% else %}
                                        Client anonyme
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if sale.table %}
                                            Table {{ sale.table.number }}
                                            {% if sale.table.location %}({{ sale.table.location }}){% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-end">{{ "%.2f"|format(sale.total) }} €</td>
                                    <td>
                                        <a href="{{ url_for('pos.sale_details', id=sale.id) }}"
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">Aucune vente aujourd'hui</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Top produits -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Top 5 des produits</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for product, quantity, revenue in top_products %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ product.name }}</h6>
                                <small>{{ quantity }} vendus</small>
                            </div>
                            <p class="mb-1">CA: {{ "%.2f"|format(revenue) }} €</p>
                        </div>
                        {% else %}
                        <div class="list-group-item">
                            Aucune donnée disponible
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}