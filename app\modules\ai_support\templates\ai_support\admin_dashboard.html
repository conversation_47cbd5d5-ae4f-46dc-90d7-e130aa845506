{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <h2>Tableau de bord - Administration Support AI</h2>
        <p class="text-muted">Vue d'ensemble des performances du support automatisé et des tickets nécessitant une attention.</p>
    </div>
</div>

<!-- Statistiques principales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-ticket-alt fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">{{ stats.total_tickets }}</h4>
                <p class="card-text">Total tickets</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ stats.open_tickets }}</h4>
                <p class="card-text">Tickets ouverts</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h4 class="text-danger">{{ stats.escalated_tickets }}</h4>
                <p class="card-text">Tickets escaladés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-comments fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ stats.active_conversations }}</h4>
                <p class="card-text">Conversations actives</p>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques détaillées -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> Performances aujourd'hui
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-success">{{ stats.tickets_today }}</h3>
                            <small class="text-muted">Nouveaux tickets</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-info">85%</h3>
                            <small class="text-muted">Taux de résolution IA</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-primary">2.3s</h3>
                            <small class="text-muted">Temps de réponse moyen</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-warning">4.2/5</h3>
                            <small class="text-muted">Satisfaction moyenne</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-robot"></i> Statut de l'IA
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Service Gemini AI</span>
                    <div>
                        <span class="badge bg-secondary me-2" id="aiStatus">
                            <i class="fas fa-question-circle"></i> Non testé
                        </span>
                        <button class="btn btn-sm btn-outline-primary" onclick="testGeminiConnection()">
                            <i class="fas fa-plug"></i> Tester
                        </button>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Modèle utilisé</span>
                    <span class="badge bg-info">gemini-2.0-flash-exp</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Confiance moyenne</span>
                    <span class="badge bg-success">78%</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Dernière mise à jour</span>
                    <small class="text-muted">Il y a 5 minutes</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tickets urgents -->
{% if urgent_tickets %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-circle text-danger"></i> 
                    Tickets nécessitant une attention
                </h5>
                <span class="badge bg-danger">{{ urgent_tickets|length }}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Numéro</th>
                                <th>Titre</th>
                                <th>Utilisateur</th>
                                <th>Statut</th>
                                <th>Priorité</th>
                                <th>Créé le</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ticket in urgent_tickets %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('ai_support.view_ticket', id=ticket.id) }}" 
                                       class="text-decoration-none fw-bold">
                                        {{ ticket.ticket_number }}
                                    </a>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="{{ ticket.title }}">
                                        {{ ticket.title }}
                                    </div>
                                </td>
                                <td>{{ ticket.user.username }}</td>
                                <td>
                                    <span class="ticket-status status-{{ ticket.status.value }}">
                                        {{ ticket.status.value.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>
                                    <span class="priority-{{ ticket.priority.value }}">
                                        <i class="fas fa-flag"></i> {{ ticket.priority.value.title() }}
                                    </span>
                                </td>
                                <td>{{ ticket.created_at | datetime }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('ai_support.view_ticket', id=ticket.id) }}" 
                                           class="btn btn-outline-primary" title="Voir">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-success" 
                                                onclick="assignTicket({{ ticket.id }})" 
                                                title="Assigner">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Graphiques et analytics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Répartition par catégorie
                </h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Évolution des tickets (7 derniers jours)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="ticketsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> Actions d'administration
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-primary" onclick="exportTickets()">
                                <i class="fas fa-download"></i> Exporter les tickets
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-info" onclick="configureAI()">
                                <i class="fas fa-cog"></i> Configurer l'IA
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-success" onclick="manageKnowledgeBase()">
                                <i class="fas fa-book"></i> Gérer la base de connaissances
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-outline-warning" onclick="viewAnalytics()">
                                <i class="fas fa-chart-line"></i> Analytics détaillées
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <button class="btn btn-outline-secondary" onclick="initSampleData()">
                                <i class="fas fa-database"></i> Initialiser données d'exemple
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <button class="btn btn-outline-danger" onclick="clearAllData()">
                                <i class="fas fa-trash"></i> Vider toutes les données
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique en secteurs pour les catégories
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
const categoryChart = new Chart(categoryCtx, {
    type: 'doughnut',
    data: {
        labels: ['Technique', 'Facturation', 'Général', 'Formation', 'Bug'],
        datasets: [{
            data: [35, 20, 25, 15, 5],
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Graphique en barres pour l'évolution
const ticketsCtx = document.getElementById('ticketsChart').getContext('2d');
const ticketsChart = new Chart(ticketsCtx, {
    type: 'line',
    data: {
        labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
        datasets: [{
            label: 'Nouveaux tickets',
            data: [12, 19, 8, 15, 22, 8, 14],
            borderColor: '#36A2EB',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.4
        }, {
            label: 'Tickets résolus',
            data: [8, 15, 12, 18, 20, 10, 16],
            borderColor: '#4BC0C0',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Test de connexion Gemini
function testGeminiConnection() {
    const statusBadge = document.getElementById('aiStatus');
    const testButton = event.target;

    // Mettre à jour l'interface
    statusBadge.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test en cours...';
    statusBadge.className = 'badge bg-warning me-2';
    testButton.disabled = true;

    // Faire la requête de test
    fetch('/support/api/test-gemini')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                statusBadge.innerHTML = '<i class="fas fa-check-circle"></i> Opérationnel';
                statusBadge.className = 'badge bg-success me-2';
                alert(`✅ Connexion réussie !\n\nModèle: ${data.model}\nRéponse: ${data.response}`);
            } else {
                statusBadge.innerHTML = '<i class="fas fa-times-circle"></i> Erreur';
                statusBadge.className = 'badge bg-danger me-2';
                alert(`❌ Erreur de connexion !\n\nErreur: ${data.error}\nDétails: ${data.details}`);
            }
        })
        .catch(error => {
            statusBadge.innerHTML = '<i class="fas fa-times-circle"></i> Erreur';
            statusBadge.className = 'badge bg-danger me-2';
            alert(`❌ Erreur de connexion !\n\nErreur: ${error.message}`);
        })
        .finally(() => {
            testButton.disabled = false;
        });
}

// Fonctions d'administration
function assignTicket(ticketId) {
    const agent = prompt('Nom de l\'agent à assigner :');
    if (agent) {
        alert(`Ticket ${ticketId} assigné à ${agent} (fonctionnalité à implémenter)`);
    }
}

function exportTickets() {
    alert('Export des tickets en cours... (fonctionnalité à implémenter)');
}

function configureAI() {
    alert('Configuration de l\'IA (fonctionnalité à implémenter)');
}

function manageKnowledgeBase() {
    window.location.href = '{{ url_for("ai_support.knowledge_base") }}';
}

function viewAnalytics() {
    alert('Analytics détaillées (fonctionnalité à implémenter)');
}

function initSampleData() {
    if (confirm('Voulez-vous initialiser la base de connaissances avec des articles d\'exemple ?')) {
        fetch('/support/api/init-sample-data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`✅ ${data.message}`);
                    location.reload();
                } else {
                    alert(`❌ Erreur: ${data.message || data.error}`);
                }
            })
            .catch(error => {
                alert(`❌ Erreur de connexion: ${error.message}`);
            });
    }
}

function clearAllData() {
    if (confirm('⚠️ ATTENTION: Cette action supprimera TOUTES les données de support (tickets, conversations, articles).\n\nÊtes-vous absolument sûr ?')) {
        if (confirm('Dernière confirmation: Voulez-vous vraiment supprimer toutes les données ?')) {
            alert('Fonctionnalité de suppression à implémenter pour des raisons de sécurité');
        }
    }
}

// Actualisation automatique des statistiques
setInterval(function() {
    // Ici, vous pourriez faire une requête AJAX pour mettre à jour les statistiques
    console.log('Actualisation des statistiques...');
}, 30000); // Toutes les 30 secondes
</script>
{% endblock %}
