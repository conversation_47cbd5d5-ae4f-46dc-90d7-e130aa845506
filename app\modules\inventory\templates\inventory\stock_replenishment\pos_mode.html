{% extends "base.html" %}

{% block title %}Mode POS - Approvisionnement{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
<script src="{{ url_for('inventory.static', filename='js/stock_replenishment_pos.js') }}"></script>
{% endblock %}

{% block content %}
<div class="pos-replenishment-container">
    <!-- En-tête Mode POS -->
    <div class="pos-replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <div class="d-flex align-items-center">
                    <h4 class="mb-0 me-4">
                        <i class="fas fa-truck-loading"></i> Mode POS - Approvisionnement
                    </h4>
                    <!-- Barre de recherche dans l'en-tête -->
                    <div class="search-section-header">
                        <div class="input-group">
                            <span class="input-group-text bg-white border-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" id="searchInput" class="form-control border-0"
                                   placeholder="Rechercher des produits ou ingrédients..."
                                   style="background: white; border-radius: 0 20px 20px 0;">
                        </div>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn btn-danger btn-sm me-2" onclick="openSupplierModal()" id="supplierButton">
                        <i class="fas fa-truck"></i> <span id="supplierButtonText">Choisir fournisseur</span>
                    </button>
                    <a href="{{ url_for('inventory.stock_replenishment_form_mode') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="fas fa-edit"></i> Mode Formulaire
                    </a>
                    <button class="btn btn-outline-light btn-sm me-2" onclick="clearCart()">
                        <i class="fas fa-trash"></i> Vider
                    </button>
                    <a href="{{ url_for('inventory.stock_replenishment_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid pos-content-reduced-margin">
        <div class="row">
            <!-- Colonne gauche - Numpad -->
            <div class="col-lg-3 slide-in-left">
                <!-- Numpad -->
                <div class="numpad-container">
                    <div class="numpad-display" id="numpadDisplay">0</div>
                    <div class="numpad-grid">
                        <button class="numpad-btn" data-value="7">7</button>
                        <button class="numpad-btn" data-value="8">8</button>
                        <button class="numpad-btn" data-value="9">9</button>
                        <button class="numpad-btn" data-value="4">4</button>
                        <button class="numpad-btn" data-value="5">5</button>
                        <button class="numpad-btn" data-value="6">6</button>
                        <button class="numpad-btn" data-value="1">1</button>
                        <button class="numpad-btn" data-value="2">2</button>
                        <button class="numpad-btn" data-value="3">3</button>
                        <button class="numpad-btn" data-value="0">0</button>
                        <button class="numpad-btn" data-value=".">.</button>
                        <button class="numpad-btn special" data-value="clear">C</button>
                    </div>
                    <div class="mt-2">
                        <button class="numpad-btn special w-100" data-value="backspace">
                            <i class="fas fa-backspace"></i> Effacer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Colonne centrale - Interface de sélection -->
            <div class="col-lg-6 fade-in-up">

                <!-- Boutons de filtrage principal -->
                <div class="main-filter-buttons mb-3">
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-outline-primary active" id="filterAll"
                                onclick="showMainFilter('all')">
                            <i class="fas fa-th-large"></i> Tous
                        </button>
                        <button type="button" class="btn btn-outline-success" id="filterProducts"
                                onclick="showMainFilter('products')">
                            <i class="fas fa-box"></i> Produits sans recettes
                        </button>
                        <button type="button" class="btn btn-outline-info" id="filterIngredients"
                                onclick="showMainFilter('ingredients')">
                            <i class="fas fa-leaf"></i> Ingrédients
                        </button>
                    </div>
                </div>

                <!-- Boutons de catégories dynamiques -->
                <div class="category-buttons mb-3" id="categoryButtons">
                    <div class="category-buttons-container">
                        <!-- Les boutons de catégories seront ajoutés ici dynamiquement -->
                    </div>
                </div>

                <!-- Grille des articles -->
                <div class="items-section">
                    <div id="itemsGrid" class="items-grid">
                        <!-- Les articles seront chargés ici via JavaScript -->
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        <nav aria-label="Navigation des articles">
                            <ul class="pagination pagination-sm" id="itemsPagination">
                                <!-- Pagination sera générée par JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Colonne droite - Ticket -->
            <div class="col-lg-3 slide-in-right">
                <!-- Ticket d'achat -->
                <div class="purchase-ticket">
                    <div class="ticket-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-receipt me-2"></i>Ticket d'Achat
                            </h6>
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-light me-2" onclick="openTicketDetailsModal()" title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <span id="ticketNumber" class="badge bg-light text-dark">
                                    #{{ now | date('%y%m%d%H%M%S') }}
                                </span>
                            </div>
                        </div>
                        <div class="ticket-info mt-2">
                            <small id="supplierInfo">
                                <i class="fas fa-truck me-1"></i>Fournisseur: Autres
                            </small><br>
                            <small>
                                <i class="fas fa-user me-1"></i>Utilisateur: {{ current_user.username }}
                            </small><br>
                            <small id="ticketDate">
                                <i class="fas fa-clock me-1"></i>{{ now | datetime }}
                            </small>
                        </div>
                    </div>

                    <div class="ticket-items" id="ticketItems">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <p>Aucun article sélectionné</p>
                        </div>
                    </div>

                    <div class="ticket-total">
                        <!-- Remise appliquée aux prix individuels -->
                        <div class="d-flex justify-content-between mb-1" id="individualDiscountRow" style="display: none;">
                            <span>Remise sur prix:</span>
                            <span id="individualDiscountAmount" class="text-success">0.00 €</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Sous-total:</span>        
                            <span id="subtotalAmount">0.00 €</span>
                        </div>
                        <!-- Remise globale -->
                        <div class="d-flex justify-content-between mb-1" id="globalDiscountRow" style="display: none;">
                            <span>Remise <span id="globalDiscountNote"></span>:</span>
                            <span id="globalDiscountAmount" class="text-warning">0.00 €</span>
                        </div>
                        <hr class="my-2">
                        <div class="d-flex justify-content-between">
                            <strong>Total à payer:</strong>
                            <strong id="totalAmount">0.00 €</strong>
                        </div>

                        <!-- Boutons d'actions -->
                        <div class="mt-2">
                            <button class="btn btn-outline-warning btn-sm w-100 mb-1" id="discountButton"
                                    onclick="StockReplenishmentPOS.openDiscountModal()">
                                <i class="fas fa-percent"></i> Appliquer une remise
                            </button>
                            <button class="btn btn-success w-100" id="payButton"
                                    onclick="openPaymentModal()" disabled>
                                <i class="fas fa-credit-card"></i> Payer la marchandise
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-credit-card"></i> Paiement Fournisseur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="payment-summary mb-4">
                    <h6>Résumé de la commande</h6>

                    <!-- Liste des articles -->
                    <div class="order-items-summary mb-3" id="paymentOrderItems">
                        <!-- Articles seront ajoutés dynamiquement -->
                    </div>

                    <!-- Totaux -->
                    <div class="border-top pt-2">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Sous-total:</span>
                            <span id="paymentSubtotal">0.00 €</span>
                        </div>
                        <!-- Remise appliquée aux prix individuels -->
                        <div class="d-flex justify-content-between mb-1" id="paymentIndividualDiscountRow" style="display: none;">
                            <span>Remise sur prix:</span>
                            <span id="paymentIndividualDiscountAmount" class="text-success">0.00 €</span>
                        </div>
                        <!-- Remise globale -->
                        <div class="d-flex justify-content-between mb-1" id="paymentGlobalDiscountRow" style="display: none;">
                            <span>Remise <span id="paymentGlobalDiscountNote"></span>:</span>
                            <span id="paymentGlobalDiscountAmount" class="text-warning">0.00 €</span>
                        </div>
                        <hr class="my-2">
                        <div class="d-flex justify-content-between">
                            <strong>Total à payer:</strong>
                            <strong id="paymentTotal" class="text-success">0.00 €</strong>
                        </div>
                    </div>
                </div>

                <div class="payment-methods">
                    <h6>Méthode de paiement</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-primary w-100 payment-method-btn" 
                                    data-method="cash_caisse">
                                <i class="fas fa-money-bill-wave"></i><br>
                                Cash depuis Caisse
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 payment-method-btn" 
                                    data-method="cheque_compte_banque">
                                <i class="fas fa-money-check"></i><br>
                                Chèque Bancaire
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 payment-method-btn" 
                                    data-method="virement_depuis_compte_banque">
                                <i class="fas fa-exchange-alt"></i><br>
                                Virement Bancaire
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 payment-method-btn" 
                                    data-method="sortie_cash_banque">
                                <i class="fas fa-university"></i><br>
                                Sortie Cash Banque
                            </button>
                        </div>
                        <div class="col-12 mb-3">
                            <button class="btn btn-outline-secondary w-100 payment-method-btn" 
                                    data-method="pay_later">
                                <i class="fas fa-clock"></i> Payer plus tard
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Options de traitement -->
                <div class="processing-options mt-4">
                    <h6>Options de traitement</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="receiveAndPay" value="receive_and_pay" checked>
                        <label class="form-check-label" for="receiveAndPay">
                            Recevoir la marchandise et payer maintenant
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType"
                               id="receivePayLater" value="receive_pay_later">
                        <label class="form-check-label" for="receivePayLater">
                            Recevoir la marchandise et payer plus tard
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="processingType" 
                               id="orderOnly" value="order_only">
                        <label class="form-check-label" for="orderOnly">
                            Bon de commande seulement (ne pas mettre à jour le stock)
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmPaymentBtn" disabled>
                    <i class="fas fa-check"></i> Confirmer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de modification de prix -->
<div class="modal fade" id="priceEditModal" tabindex="-1" aria-labelledby="priceEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="priceEditModalLabel">
                    <i class="fas fa-edit"></i> Modifier le prix d'achat
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Article:</label>
                    <div id="editItemName" class="fw-bold"></div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Prix référentiel:</label>
                        <div id="editReferencePrice" class="text-muted"></div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Prix actuel:</label>
                        <div id="editCurrentPrice" class="text-info"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="newPrice" class="form-label">Nouveau prix d'achat:</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="newPrice" step="0.01" min="0">
                        <span class="input-group-text">€</span>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="priceChangeReason" class="form-label">Raison du changement:</label>
                    <select class="form-select" id="priceChangeReason">
                        <option value="">Sélectionner une raison</option>
                        <option value="market_increase">Augmentation du marché</option>
                        <option value="market_decrease">Baisse du marché</option>
                        <option value="supplier_change">Changement de fournisseur</option>
                        <option value="quality_change">Changement de qualité</option>
                        <option value="promotion">Promotion fournisseur</option>
                        <option value="other">Autre</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmPriceChange">
                    <i class="fas fa-check"></i> Confirmer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de sélection des fournisseurs -->
<div class="modal fade" id="supplierModal" tabindex="-1" aria-labelledby="supplierModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content supplier-modal-content">
            <div class="modal-header supplier-modal-header">
                <h5 class="modal-title" id="supplierModalLabel">
                    <i class="fas fa-truck me-2"></i>Sélection du fournisseur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body supplier-modal-body">
                <div class="supplier-selection-container">
                    <div class="supplier-field-group">
                        <label class="supplier-field-label">
                            <i class="fas fa-tags me-2"></i>Catégorie de fournisseur
                        </label>
                        <select id="modalSupplierCategorySelect" class="form-select supplier-select">
                            <option value="0">Toutes les catégories</option>
                            {% for category in supplier_categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="supplier-field-group">
                        <label class="supplier-field-label">
                            <i class="fas fa-truck me-2"></i>Fournisseur
                        </label>
                        <select id="modalSupplierSelect" class="form-select supplier-select">
                            <option value="0">Aucun fournisseur</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer supplier-modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Annuler
                </button>
                <button type="button" class="btn btn-success" onclick="applySupplierSelection()">
                    <i class="fas fa-check me-2"></i>Appliquer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de remise -->
<div class="modal fade" id="discountModal" tabindex="-1" aria-labelledby="discountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="discountModalLabel">
                    <i class="fas fa-percent"></i> Appliquer une remise
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Type de remise:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="discountType" id="discountAmount" value="amount" checked>
                        <label class="form-check-label" for="discountAmount">
                            Montant fixe (€)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="discountType" id="discountPercentage" value="percentage">
                        <label class="form-check-label" for="discountPercentage">
                            Pourcentage (%)
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="discountValue" class="form-label">Valeur de la remise:</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="discountValue" step="0.01" min="0">
                        <span class="input-group-text" id="discountUnit">€</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="applyToItems">
                        <label class="form-check-label" for="applyToItems">
                            Appliquer la remise aux prix d'achat individuels
                        </label>
                        <small class="form-text text-muted">
                            Si coché, la remise sera répartie sur les prix d'achat de chaque article
                        </small>
                    </div>
                </div>
                <div class="alert alert-info">
                    <strong>Sous-total actuel:</strong> <span id="currentSubtotal">0.00 €</span><br>
                    <strong>Remise calculée:</strong> <span id="calculatedDiscount">0.00 €</span><br>
                    <strong>Nouveau total:</strong> <span id="newTotal">0.00 €</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" id="confirmDiscount">
                    <i class="fas fa-check"></i> Appliquer la remise
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modale des détails du ticket -->
<div class="modal fade" id="ticketDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-receipt"></i> Détails du Ticket d'Achat
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle"></i> Informations générales</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Numéro de ticket:</strong></td>
                                <td id="modalTicketNumber">-</td>
                            </tr>
                            <tr>
                                <td><strong>Fournisseur:</strong></td>
                                <td id="modalSupplier">-</td>
                            </tr>
                            <tr>
                                <td><strong>Utilisateur:</strong></td>
                                <td>{{ current_user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date:</strong></td>
                                <td id="modalDate">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-calculator"></i> Résumé financier</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Sous-total:</strong></td>
                                <td id="modalSubtotal">0.00 €</td>
                            </tr>
                            <tr>
                                <td><strong>Remise totale:</strong></td>
                                <td id="modalDiscount" class="text-success">0.00 €</td>
                            </tr>
                            <tr class="table-primary">
                                <td><strong>Total à payer:</strong></td>
                                <td id="modalTotal"><strong>0.00 €</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <h6><i class="fas fa-list"></i> Articles commandés</h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Prix unitaire</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody id="modalItemsList">
                            <tr>
                                <td colspan="4" class="text-center text-muted">Aucun article dans le ticket</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" onclick="printTicket()">
                    <i class="fas fa-print"></i> Imprimer
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Données initiales pour JavaScript
window.stockReplenishmentData = {
    products: {{ products_json | safe }},
    ingredients: {{ ingredients_json | safe }},
    suppliers: {{ suppliers_json | safe }},
    productCategories: {{ product_categories_json | safe }},
    ingredientCategories: {{ ingredient_categories_json | safe }}
};
</script>
{% endblock %}
