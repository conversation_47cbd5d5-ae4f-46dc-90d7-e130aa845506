from app.extensions import db, login
from flask_login import UserMixin, AnonymousUserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import enum

# Anonymous User for Flask-Login
class Anonymous(AnonymousUserMixin):
    @property
    def is_system_admin(self):
        return False
    
    @property
    def is_owner(self):
        return False
    
    @property
    def is_admin(self):
        return False
    
    @property
    def is_manager(self):
        return False
    
    def can_manage_cash_register(self):
        return False
    
    def can_access_cash_register(self):
        return False
    
    def has_permission(self, permission):
        return False
    
    def can_access_reports(self):
        return False
    
    def can_manage_inventory(self):
        return False
    
    def can_process_sales(self):
        return False
    
    def can_access_kitchen(self):
        return False
    
    def can_manage_customers(self):
        return False
    
    def can_manage_promotions(self):
        return False
    
    def can_manage_tables(self):
        return False
    
    def can_manage_expenses(self):
        return False

class UserRole(str, enum.Enum):
    SYSTEM_ADMIN = 'SYSTEM_ADMIN'
    OWNER = 'OWNER'
    ADMIN = 'ADMIN'
    MANAGER = 'MANAGER'
    EMPLOYEE = 'EMPLOYEE'
    CASHIER = 'CASHIER'
    KITCHEN = 'KITCHEN'
    ACCOUNTANT = 'ACCOUNTANT'
    WAITER = 'WAITER'

    def __str__(self):
        return self.value

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    role = db.Column(db.Enum(UserRole), default=UserRole.EMPLOYEE)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    timezone = db.Column(db.String(50), default='Europe/Paris')
    
    # Relations
    created_users = db.relationship('User', backref=db.backref('created_by', remote_side=[id]), foreign_keys=[created_by_id])
    owned_users = db.relationship('User', backref=db.backref('owner', remote_side=[id]), foreign_keys=[owner_id])
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    @property
    def is_system_admin(self):
        """Vérifie si l'utilisateur est l'administrateur système"""
        return self.role == UserRole.SYSTEM_ADMIN
    
    @property
    def is_owner(self):
        """Vérifie si l'utilisateur est le propriétaire"""
        return self.role == UserRole.OWNER
    
    @property
    def is_admin(self):
        """Vérifie si l'utilisateur est un administrateur"""
        return self.role == UserRole.ADMIN
    
    @property
    def is_manager(self):
        """Vérifie si l'utilisateur est un manager"""
        return self.role == UserRole.MANAGER
    
    @property
    def is_employee(self):
        """Vérifie si l'utilisateur est un employé"""
        return self.role == UserRole.EMPLOYEE
    
    @property
    def is_cashier(self):
        """Vérifie si l'utilisateur est un caissier"""
        return self.role == UserRole.CASHIER
    
    @property
    def is_kitchen(self):
        """Vérifie si l'utilisateur est du personnel de cuisine"""
        return self.role == UserRole.KITCHEN
    
    def can_access_reports(self):
        """Vérifie si l'utilisateur peut accéder aux rapports"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER]
    
    def can_manage_inventory(self):
        """Vérifie si l'utilisateur peut gérer l'inventaire"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER]
    
    def can_process_sales(self):
        """Vérifie si l'utilisateur peut gérer les ventes"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER]
    
    def can_access_kitchen(self):
        """Vérifie si l'utilisateur peut accéder à la cuisine"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.KITCHEN]
    
    def can_manage_customers(self):
        """Vérifie si l'utilisateur peut gérer les clients"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER]
    
    def can_manage_promotions(self):
        """Vérifie si l'utilisateur peut gérer les promotions"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER]
    
    def can_manage_tables(self):
        """Vérifie si l'utilisateur peut gérer les tables"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER]
    
    def can_manage_expenses(self):
        """Vérifie si l'utilisateur peut gérer les dépenses"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER]

    def can_manage_employees(self):
        """Vérifie si l'utilisateur peut gérer les employés"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER]

    def can_view_employee_reports(self):
        """Vérifie si l'utilisateur peut voir les rapports d'employés"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.ACCOUNTANT]

    def can_manage_payroll(self):
        """Vérifie si l'utilisateur peut gérer la paie"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.ACCOUNTANT]
    
    def can_access_cash_register(self):
        """Vérifie si l'utilisateur peut accéder à la caisse"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER]
    
    def can_manage_cash_register(self):
        """Vérifie si l'utilisateur peut gérer la caisse"""
        return self.is_system_admin or self.is_owner or self.role in [UserRole.ADMIN, UserRole.MANAGER]
    
    def has_permission(self, permission):
        """Vérifie si l'utilisateur a une permission donnée"""
        if self.is_system_admin or self.is_owner:
            return True
        
        permission_map = {
            'can_process_sales': [UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER],
            'can_access_cash_register': [UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER],
            'can_manage_cash_register': [UserRole.ADMIN, UserRole.MANAGER],
            'can_view_cash_history': [UserRole.ADMIN, UserRole.MANAGER, UserRole.ACCOUNTANT],
            'can_export_cash_history': [UserRole.ADMIN, UserRole.MANAGER, UserRole.ACCOUNTANT],
            'can_manage_inventory': [UserRole.ADMIN, UserRole.MANAGER],
            'can_manage_expenses': [UserRole.ADMIN, UserRole.MANAGER],
            'can_access_reports': [UserRole.ADMIN, UserRole.MANAGER, UserRole.ACCOUNTANT],
            'can_manage_customers': [UserRole.ADMIN, UserRole.MANAGER],
            'can_manage_promotions': [UserRole.ADMIN, UserRole.MANAGER],
            'can_access_kitchen': [UserRole.ADMIN, UserRole.MANAGER, UserRole.KITCHEN],
            'can_manage_tables': [UserRole.ADMIN, UserRole.MANAGER, UserRole.WAITER],
            'can_manage_employees': [UserRole.ADMIN, UserRole.MANAGER],
            'can_view_employee_reports': [UserRole.ADMIN, UserRole.MANAGER, UserRole.ACCOUNTANT],
            'can_manage_payroll': [UserRole.ADMIN, UserRole.ACCOUNTANT]
        }
        
        return permission in permission_map and self.role in permission_map[permission]

    @property
    def get_owner_id(self):
        """Retourne l'ID de l'owner de l'utilisateur ou l'ID du SYSTEM_ADMIN pour ses propres données"""
        if self.role == UserRole.SYSTEM_ADMIN:
            return self.id  # SYSTEM_ADMIN voit ses propres données
        if self.role == UserRole.OWNER:
            return self.id  # OWNER voit ses propres données
        
        # Pour les autres utilisateurs (ADMIN, MANAGER, CASHIER, etc.)
        if self.owner_id:
            owner = User.query.get(self.owner_id)
            if owner and owner.role == UserRole.OWNER:
                return self.owner_id  # Retourne l'ID de l'OWNER
            elif owner and owner.role == UserRole.SYSTEM_ADMIN:
                return self.owner_id  # Retourne l'ID du SYSTEM_ADMIN
        
        # Si pas d'owner_id, on vérifie created_by
        if self.created_by:
            if self.created_by.role == UserRole.OWNER:
                return self.created_by_id
            elif self.created_by.role == UserRole.SYSTEM_ADMIN:
                return self.created_by_id
        
        return None
    
    def __repr__(self):
        return f'<User {self.username}>'

@login.user_loader
def load_user(id):
    return User.query.get(int(id)) 