"""Add timezone field to customer_users

Revision ID: 9c5a86dcc733
Revises: c0d8c480b97b
Create Date: 2025-09-23 18:33:26.080011

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9c5a86dcc733'
down_revision = 'c0d8c480b97b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('customer_users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('timezone', sa.String(length=50), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('customer_users', schema=None) as batch_op:
        batch_op.drop_column('timezone')

    # ### end Alembic commands ###
