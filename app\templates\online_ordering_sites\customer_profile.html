{% extends "online_ordering_sites/base.html" %}

{% block title %}Mon Profil - {{ site.site_name }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <!-- Informations du profil -->
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Mon Profil</h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="avatar-circle mx-auto mb-3" style="width: 80px; height: 80px; background-color: #007bff; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">
                            {{ customer.first_name[0]|upper }}{{ customer.last_name[0]|upper }}
                        </div>
                        <h5 class="mb-0">{{ customer.first_name }} {{ customer.last_name }}</h5>
                        <p class="text-muted">{{ customer.email }}</p>
                    </div>
                    
                    <hr>
                    
                    <div class="text-start">
                        {% if customer.phone %}
                        <p><i class="fas fa-phone text-muted me-2"></i> {{ customer.phone }}</p>
                        {% endif %}
                        
                        <p><i class="fas fa-calendar text-muted me-2"></i> Membre depuis {{ customer.created_at | date if customer.created_at else 'N/A' }}</p>
                        
                        {% if customer.last_login %}
                        <p><i class="fas fa-clock text-muted me-2"></i> Dernière connexion {{ customer.last_login | datetime('%d/%m/%Y', 'à %H:%M') }}</p>
                        {% endif %}
                    </div>
                    
                    <div class="mt-3">
                        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                            <i class="fas fa-edit"></i> Modifier mon profil
                        </button>
                        <a href="{{ url_for('online_ordering_sites.customer_orders') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-history"></i> Voir toutes mes commandes
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistiques et commandes récentes -->
        <div class="col-md-8">
            <!-- Statistiques -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>{{ orders|length }}</h3>
                            <p class="mb-0">Commandes totales</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>{{ orders|selectattr('status.value', 'equalto', 'delivered')|list|length }}</h3>
                            <p class="mb-0">Commandes livrées</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>{{ "%.2f"|format(orders|sum(attribute='total_amount')) }}€</h3>
                            <p class="mb-0">Total dépensé</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Commandes récentes -->
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> Mes Commandes Récentes</h5>
                    <a href="{{ url_for('online_ordering_sites.customer_orders') }}" class="btn btn-sm btn-outline-primary">
                        Voir tout
                    </a>
                </div>
                <div class="card-body">
                    {% if orders %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>N° Commande</th>
                                        <th>Date</th>
                                        <th>Statut</th>
                                        <th>Montant</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in orders[:5] if orders %}
                                    <tr>
                                        <td>
                                            <strong>#{{ order.order_number }}</strong>
                                            <br>
                                            <small class="text-muted">
                                                {% if order.order_type.value == 'delivery' %}
                                                    <i class="fas fa-truck"></i> Livraison
                                                {% elif order.order_type.value == 'pickup' %}
                                                    <i class="fas fa-shopping-bag"></i> À emporter
                                                {% elif order.order_type.value == 'dine_in' %}
                                                    <i class="fas fa-utensils"></i> Sur place
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>{{ order.ordered_at | datetime }}</td>
                                        <td>
                                            {% if order.status.value == 'pending' %}
                                                <span class="badge bg-warning">En attente</span>
                                            {% elif order.status.value == 'confirmed' %}
                                                <span class="badge bg-info">Confirmée</span>
                                            {% elif order.status.value == 'preparing' %}
                                                <span class="badge bg-primary">En préparation</span>
                                            {% elif order.status.value == 'ready' %}
                                                <span class="badge bg-success">Prête</span>
                                            {% elif order.status.value == 'out_for_delivery' %}
                                                <span class="badge bg-warning">En livraison</span>
                                            {% elif order.status.value == 'delivered' %}
                                                <span class="badge bg-success">Livrée</span>
                                            {% elif order.status.value == 'cancelled' %}
                                                <span class="badge bg-danger">Annulée</span>
                                            {% endif %}
                                        </td>
                                        <td><strong>{{ "%.2f"|format(order.total_amount) }}€</strong></td>
                                        <td>
                                            <a href="{{ url_for('online_ordering_sites.track_order', order_number=order.order_number) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> Suivre
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5>Aucune commande</h5>
                            <p class="text-muted">Vous n'avez pas encore passé de commande.</p>
                            <a href="{{ url_for('online_ordering_sites.menu') }}" class="btn btn-primary">
                                <i class="fas fa-utensils"></i> Découvrir le menu
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour modifier le profil -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                {{ form.hidden_tag() }}
                <div class="modal-header">
                    <h5 class="modal-title" id="editProfileModalLabel">
                        <i class="fas fa-edit"></i> Modifier mon profil
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.first_name.label(class="form-label") }}
                                {{ form.first_name(class="form-control") }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.first_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.last_name.label(class="form-label") }}
                                {{ form.last_name(class="form-control") }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.phone.label(class="form-label") }}
                        {{ form.phone(class="form-control") }}
                        {% if form.phone.errors %}
                            <div class="text-danger">
                                {% for error in form.phone.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.timezone.label(class="form-label") }}
                        {{ form.timezone(class="form-select") }}
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            Choisissez votre fuseau horaire pour afficher les dates et heures correctement
                        </div>
                        {% if form.timezone.errors %}
                            <div class="text-danger">
                                {% for error in form.timezone.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <hr>

                    <div class="mb-3">
                        <label class="form-label">Notifications</label>
                        <div class="form-check">
                            {{ form.email_notifications(class="form-check-input") }}
                            {{ form.email_notifications.label(class="form-check-label") }}
                        </div>
                        <div class="form-check">
                            {{ form.sms_notifications(class="form-check-input") }}
                            {{ form.sms_notifications.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
