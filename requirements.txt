# Core Flask dependencies
Flask
Flask-SQLAlchemy
Flask-Migrate
Flask-Login
Flask-WTF
Flask-Caching
flask-redis
flask-socketio
Werkzeug

# Database
SQLAlchemy
alembic

# Security
bcrypt
PyJWT

# Forms and validation
WTForms
email_validator

# Utilities
Jinja2
MarkupSafe
itsdangerous
click
blinker

# Data processing
pandas
numpy
openpyxl
XlsxWriter

# Image processing
Pillow

# PDF generation
reportlab

# HTTP requests
requests
urllib3
certifi

# Redis
redis

# Development tools
python-dotenv
Flask-DebugToolbar
flask-shell-ipython

# Testing
pytest
pytest-flask
pytest-cov
pytest-html
pytest-metadata
pytest-selenium
pytest-variables
pytest-xdist
pytest-base-url
coverage

# Selenium for testing
selenium
webdriver-manager

# Fake data generation
faker

# AI and Machine Learning dependencies
google-generativeai
google-ai-generativelanguage
google-auth
google-auth-oauthlib
google-auth-httplib2

# Additional utilities for AI support
tenacity
python-dateutil
pytz

# Other dependencies
colorama
packaging
six
typing_extensions
tzdata