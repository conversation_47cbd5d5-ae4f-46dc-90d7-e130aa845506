{% extends "base.html" %}
{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap5.min.css">
<style>
.chart-area {
    position: relative;
    height: 400px;
    margin: 20px 0;
}
.chart-pie {
    position: relative;
    height: 400px;
    margin: 20px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <button type="button" class="btn btn-outline-primary" onclick="exportExpenseReport()" title="Exporter les dépenses" aria-label="Exporter les dépenses">
                <i class="fas fa-file-export" aria-hidden="true"></i> Exporter
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                <i class="fas fa-filter"></i> Filtrer
            </button>
        </div>
    </div>

    <!-- Summary Cards Row -->
    <div class="row">
        <!-- Total Expenses Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Total des dépenses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_amount|format_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Number of Expenses Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Nombre de dépenses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ expenses|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-receipt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Expense Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Moyenne par dépense</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ (total_amount / expenses|length if expenses else 0)|format_currency }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daily Average Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Moyenne journalière</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ (total_amount / ((end_date - start_date).days + 1))|format_currency }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Liste des dépenses</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="expensesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Catégorie</th>
                            <th>Montant</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for expense in expenses %}
                        <tr>
                            <td>{{ expense.date | date }}</td>
                            <td>{{ expense.description }}</td>
                            <td>{{ expense.category.name if expense.category else "Sans catégorie" }}</td>
                            <td>{{ expense.amount|format_currency }}</td>
                            <td>
                                <button type="button" class="btn btn-info btn-sm" onclick="viewExpense('{{ expense.id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm" onclick="editExpense('{{ expense.id }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteExpense('{{ expense.id }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Expense Trend -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Évolution des dépenses</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="expenseTrendChart"></canvas>
                    </div>
                    <!-- Données pour le graphique -->
                    <div class="d-none">
                        <div id="expenseDates">{{ dates|tojson|safe }}</div>
                        <div id="expenseValues">{{ expense_values|tojson|safe }}</div>
                    </div>
                    <!-- Message quand pas de données -->
                    {% if expense_values|sum == 0 %}
                    <div class="text-center mt-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Aucune dépense enregistrée pour cette période.
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Expense by Category -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Répartition par catégorie</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="expenseCategoryChart"></canvas>
                    </div>
                    <!-- Données pour le graphique -->
                    <div class="d-none">
                        <div id="categoryLabels">{{ category_labels|tojson|safe }}</div>
                        <div id="categoryData">{{ category_data|tojson|safe }}</div>
                        <div id="categoryColors">{{ category_colors|tojson|safe }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filtrer les dépenses</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="filterForm" method="GET">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label" for="period">Période</label>
                        <select class="form-select" name="period" id="period" aria-label="Sélectionner la période">
                            <option value="today" {% if period == 'today' %}selected{% endif %}>Aujourd'hui</option>
                            <option value="week" {% if period == 'week' %}selected{% endif %}>Cette semaine</option>
                            <option value="month" {% if period == 'month' %}selected{% endif %}>Ce mois</option>
                            <option value="year" {% if period == 'year' %}selected{% endif %}>Cette année</option>
                            <option value="custom" {% if period == 'custom' %}selected{% endif %}>Personnalisé</option>
                        </select>
                    </div>
                    <div id="customDateRange" class="{% if period != 'custom' %}d-none{% endif %}">
                        <div class="mb-3">
                            <label class="form-label">Date de début</label>
                            <input type="date" class="form-control" name="start_date" value="{{ start_date | date('%Y-%m-%d') }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date de fin</label>
                            <input type="date" class="form-control" name="end_date" value="{{ end_date | date('%Y-%m-%d') }}">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Catégorie</label>
                        <select class="form-select" name="category">
                            <option value="">Toutes les catégories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Appliquer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Scripts spécifiques à la page des rapports -->
<script src="{{ url_for('reports.static', filename='js/reports.js') }}"></script>
<!-- DataTables Buttons (non inclus dans base.html) -->
<script src="https://cdn.datatables.net/buttons/2.0.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.0.1/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.0.1/js/buttons.html5.min.js"></script>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');

    // Initialisation des graphiques
    // --- Graphique: Évolution des dépenses ---
    const trendCtx = document.getElementById('expenseTrendChart');
    if (trendCtx) {
        try {
            const expenseDates = JSON.parse(document.getElementById('expenseDates').textContent);
            const expenseValues = JSON.parse(document.getElementById('expenseValues').textContent);

            if (expenseValues.reduce((a, b) => a + b, 0) > 0) {
                new Chart(trendCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: expenseDates,
                        datasets: [{
                            label: 'Dépenses',
                            data: expenseValues,
                            borderColor: 'rgba(220, 53, 69, 1)',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            fill: true,
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value + ' €';
                                    }
                                }
                            }
                        }
                    }
                });
            }
        } catch (e) {
            console.error("Erreur lors de l'initialisation du graphique d'évolution:", e);
        }
    }

    // --- Graphique: Répartition par catégorie ---
    const categoryCtx = document.getElementById('expenseCategoryChart');
    if (categoryCtx) {
        try {
            const categoryLabels = JSON.parse(document.getElementById('categoryLabels').textContent);
            const categoryData = JSON.parse(document.getElementById('categoryData').textContent);
            const categoryColors = JSON.parse(document.getElementById('categoryColors').textContent);

            if (categoryData.length > 0 && categoryData.reduce((a, b) => a + b, 0) > 0) {
                new Chart(categoryCtx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: categoryLabels,
                        datasets: [{
                            data: categoryData,
                            backgroundColor: categoryColors,
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { position: 'bottom' } }
                    }
                });
            }
        } catch (e) {
            console.error("Erreur lors de l'initialisation du graphique des catégories:", e);
        }
    }

    // Fonction pour exporter le rapport
    window.exportExpenseReport = function() {
        var url = new URL(window.location.href);
        url.pathname = url.pathname + '/export';
        window.location.href = url.toString();
    };
});
</script>
{% endblock %}