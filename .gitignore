# ==========================================
# Project-Specific Rules
# ==========================================

# Instance folder containing sensitive configuration and the database
/instance/

# Directory for user-uploaded files
/app/static/uploads/

# Generated statistics file
loc_stats.csv

# Backups directory
backups/

# Database files
*.db
*.sqlite3
*.sqlite

# Markdown files
MD/

# 
count_loc_stats.py
loc_stats.csv

# ==========================================
# General Rules
# ==========================================

# Environment variables
.env
*.env

# Virtual environments
.venv/
venv/
ENV/

# Python cache & compiled files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
.pytest_cache/
.hypothesis/

# Translations
*.mo
*.pot

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# IDE/Editor specific files
.vscode/
.idea/
.spyderproject
.spyderworkspace
.ropeproject

# OS-specific files
.DS_Store
Thumbs.db