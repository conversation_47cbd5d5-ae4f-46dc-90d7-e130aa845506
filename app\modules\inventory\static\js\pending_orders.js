// JavaScript pour la gestion des commandes en attente
const PendingOrders = {
    currentOrderId: null,
    filters: {
        supplier: '',
        date: '',
        status: ''
    },

    init: function() {
        this.initEventListeners();
        this.initAutoRefresh();
        this.initKeyboardShortcuts();
        this.initTooltips();
    },

    initEventListeners: function() {
        // Filtres avec application automatique
        document.getElementById('supplierFilter').addEventListener('change', () => {
            this.filters.supplier = document.getElementById('supplierFilter').value;
            this.applyFilters();
        });

        document.getElementById('dateFilter').addEventListener('change', () => {
            this.filters.date = document.getElementById('dateFilter').value;
            this.applyFilters();
        });

        document.getElementById('statusFilter').addEventListener('change', () => {
            this.filters.status = document.getElementById('statusFilter').value;
            this.applyFilters();
        });

        // Recherche en temps réel
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                this.applyFilters();
            });
        }

        // Confirmation de réception partielle
        const confirmBtn = document.getElementById('confirmPartialReceiveBtn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.confirmPartialReceive();
            });
        }

        // Recherche en temps réel
        this.initSearchFunctionality();
    },

    initSearchFunctionality: function() {
        // Ajouter une barre de recherche si elle n'existe pas
        const searchContainer = document.querySelector('.card-body .row');
        if (searchContainer && !document.getElementById('searchInput')) {
            const searchDiv = document.createElement('div');
            searchDiv.className = 'col-md-12 mb-3';
            searchDiv.innerHTML = `
                <label class="form-label">Recherche</label>
                <input type="text" class="form-control" id="searchInput" 
                       placeholder="Rechercher par référence, fournisseur, article...">
            `;
            searchContainer.insertBefore(searchDiv, searchContainer.firstChild);

            // Ajouter l'événement de recherche
            document.getElementById('searchInput').addEventListener('input', (e) => {
                this.performSearch(e.target.value);
            });
        }
    },

    performSearch: function(query) {
        const orderCards = document.querySelectorAll('.order-card');
        const searchTerm = query.toLowerCase();

        orderCards.forEach(card => {
            const cardText = card.textContent.toLowerCase();
            const isVisible = cardText.includes(searchTerm);
            card.style.display = isVisible ? 'block' : 'none';
        });
    },

    applyFilters: function() {
        const orderCards = document.querySelectorAll('.order-card');
        const searchTerm = document.getElementById('searchInput') ?
                          document.getElementById('searchInput').value.toLowerCase() : '';

        orderCards.forEach(card => {
            let show = true;

            // Filtre par fournisseur
            if (this.filters.supplier && card.dataset.supplier !== this.filters.supplier) {
                show = false;
            }

            // Filtre par date
            if (this.filters.date && card.dataset.date !== this.filters.date) {
                show = false;
            }

            // Filtre par statut
            if (this.filters.status && card.dataset.status !== this.filters.status) {
                show = false;
            }

            // Filtre par recherche textuelle
            if (searchTerm && show) {
                const reference = (card.dataset.reference || '').toLowerCase();
                const supplierName = (card.dataset.supplierName || '').toLowerCase();
                const cardText = card.textContent.toLowerCase();

                if (!reference.includes(searchTerm) &&
                    !supplierName.includes(searchTerm) &&
                    !cardText.includes(searchTerm)) {
                    show = false;
                }
            }

            card.style.display = show ? 'block' : 'none';
        });

        this.updateFilterStats();
    },

    clearFilters: function() {
        this.filters = { supplier: '', date: '', status: '' };
        
        document.getElementById('supplierFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('statusFilter').value = '';
        
        if (document.getElementById('searchInput')) {
            document.getElementById('searchInput').value = '';
        }
        
        document.querySelectorAll('.order-card').forEach(card => {
            card.style.display = 'block';
        });

        this.updateFilterStats();
    },

    updateFilterStats: function() {
        const visibleCards = document.querySelectorAll('.order-card[style="display: block"], .order-card:not([style])');
        const totalCards = document.querySelectorAll('.order-card');
        
        // Mettre à jour le compteur si il existe
        let counterElement = document.getElementById('filterCounter');
        if (!counterElement) {
            counterElement = document.createElement('small');
            counterElement.id = 'filterCounter';
            counterElement.className = 'text-muted';
            
            const headerActions = document.querySelector('.header-actions');
            if (headerActions) {
                headerActions.insertBefore(counterElement, headerActions.firstChild);
            }
        }
        
        counterElement.textContent = `${visibleCards.length} sur ${totalCards.length} commandes affichées`;
    },

    markAsReceived: function(orderId) {
        if (!confirm('Êtes-vous sûr de vouloir marquer cette commande comme entièrement reçue ?')) {
            return;
        }

        this.showLoading(orderId);

        fetch(`/inventory/stock-replenishment/orders/${orderId}/receive`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': this.getCSRFToken(),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            this.hideLoading(orderId);
            
            if (data.success) {
                this.showSuccess('Commande marquée comme reçue et stock mis à jour');
                this.removeOrderCard(orderId);
                this.updateStats();
            } else {
                this.showError(data.error || 'Erreur inconnue');
            }
        })
        .catch(error => {
            this.hideLoading(orderId);
            console.error('Erreur:', error);
            this.showError('Erreur de connexion');
        });
    },

    partialReceive: function(orderId) {
        // Ensure orderId is valid
        if (!orderId || orderId === 'null' || orderId === null) {
            this.showError('Erreur: ID de commande invalide');
            return;
        }

        this.currentOrderId = orderId;
        console.log('Setting currentOrderId to:', orderId); // Debug log

        fetch(`/inventory/api/purchase-order/${orderId}/items`)
            .then(response => response.json())
            .then(data => {
                this.displayPartialReceiveModal(data);
            })
            .catch(error => {
                console.error('Erreur:', error);
                this.showError('Erreur lors du chargement des détails');
            });
    },

    displayPartialReceiveModal: function(orderData) {
        const content = document.getElementById('partialReceiveContent');
        content.innerHTML = `
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> Réception Partielle - Commande ${orderData.reference}</h6>
                <p class="mb-2">Saisissez les quantités que vous recevez <strong>maintenant</strong> pour chaque article :</p>
                <small class="text-muted">
                    <i class="fas fa-lightbulb"></i>
                    Les quantités saisies s'ajouteront aux quantités déjà reçues.
                    Vous pouvez effectuer plusieurs réceptions partielles jusqu'à compléter la commande.
                </small>
            </div>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Commandé</th>
                            <th>Déjà reçu</th>
                            <th>Restant</th>
                            <th>Recevoir maintenant</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orderData.items.map(item => `
                            <tr>
                                <td>
                                    <strong>${item.name}</strong>
                                    ${item.notes ? `<br><small class="text-muted">${item.notes}</small>` : ''}
                                </td>
                                <td>
                                    <span class="badge bg-primary">${item.quantity} ${item.unit || ''}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">${item.received_quantity || 0} ${item.unit || ''}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning text-dark">${item.quantity - (item.received_quantity || 0)} ${item.unit || ''}</span>
                                </td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input type="number" class="form-control receive-quantity"
                                               data-item-id="${item.id}"
                                               min="0"
                                               max="${item.quantity - (item.received_quantity || 0)}"
                                               step="0.01"
                                               value="0"
                                               placeholder="Quantité à recevoir">
                                        <span class="input-group-text">${item.unit || ''}</span>
                                    </div>
                                    <small class="text-muted">Max: ${item.quantity - (item.received_quantity || 0)} ${item.unit || ''}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary"
                                                onclick="PendingOrders.setMaxQuantity('${item.id}', ${item.quantity - (item.received_quantity || 0)})"
                                                title="Recevoir tout le restant">
                                            <i class="fas fa-arrow-up"></i> Tout
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary"
                                                onclick="PendingOrders.setZeroQuantity('${item.id}')"
                                                title="Ne rien recevoir maintenant">
                                            <i class="fas fa-times"></i> Rien
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            <div class="mt-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="updateStockCheckbox" checked>
                    <label class="form-check-label" for="updateStockCheckbox">
                        Mettre à jour automatiquement les stocks
                    </label>
                </div>
            </div>
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('partialReceiveModal'));
        modal.show();
    },

    setMaxQuantity: function(itemId, maxQuantity) {
        const input = document.querySelector(`[data-item-id="${itemId}"]`);
        if (input) {
            input.value = maxQuantity;
        }
    },

    setZeroQuantity: function(itemId) {
        const input = document.querySelector(`[data-item-id="${itemId}"]`);
        if (input) {
            input.value = 0;
        }
    },

    clearSearch: function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
            this.applyFilters();
        }
    },

    editOrder: function(orderId) {
        // Rediriger vers la page d'édition de commande
        window.location.href = `/inventory/stock-replenishment/orders/${orderId}/edit`;
    },

    setMaxQuantity: function(itemId, maxQuantity) {
        const input = document.querySelector(`input[data-item-id="${itemId}"]`);
        if (input) {
            input.value = maxQuantity;
        }
    },

    setZeroQuantity: function(itemId) {
        const input = document.querySelector(`input[data-item-id="${itemId}"]`);
        if (input) {
            input.value = 0;
        }
    },

    confirmPartialReceive: function() {
        const quantities = {};
        let hasQuantities = false;
        let totalItems = 0;

        document.querySelectorAll('.receive-quantity').forEach(input => {
            const itemId = input.dataset.itemId;
            const quantity = parseFloat(input.value) || 0;
            const maxQuantity = parseFloat(input.max) || 0;

            // Validation des quantités
            if (quantity > maxQuantity) {
                this.showError(`La quantité pour l'article ne peut pas dépasser ${maxQuantity}`);
                input.focus();
                return;
            }

            quantities[itemId] = quantity;
            totalItems++;
            if (quantity > 0) {
                hasQuantities = true;
            }
        });

        if (totalItems === 0) {
            this.showError('Aucun article trouvé dans la commande');
            return;
        }

        if (!hasQuantities) {
            this.showError('Veuillez saisir au moins une quantité à recevoir');
            return;
        }

        const updateStockCheckbox = document.getElementById('updateStockCheckbox');
        const updateStock = updateStockCheckbox ? updateStockCheckbox.checked : true;

        // Ensure currentOrderId is not null
        if (!this.currentOrderId || this.currentOrderId === 'null') {
            this.showError('Erreur: ID de commande manquant ou invalide');
            return;
        }

        // Disable the confirm button to prevent double submission
        const confirmBtn = document.getElementById('confirmPartialReceiveBtn');
        if (confirmBtn) {
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Traitement...';
        }

        fetch(`/inventory/stock-replenishment/orders/${this.currentOrderId}/partial-receive`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify({
                quantities: quantities,
                update_stock: updateStock
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showSuccess('Réception partielle enregistrée' + (updateStock ? ' et stock mis à jour' : ''));
                bootstrap.Modal.getInstance(document.getElementById('partialReceiveModal')).hide();
                location.reload();
            } else {
                this.showError(data.error || 'Erreur inconnue');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            this.showError('Erreur de connexion');
        });
    },

    cancelOrder: function(orderId) {
        // Get order details for the modal
        const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
        const orderReference = orderCard ? orderCard.dataset.reference : `#${orderId}`;
        const supplierName = orderCard ? orderCard.dataset.supplierName : 'Inconnu';

        // Show confirmation modal
        const modal = new bootstrap.Modal(document.getElementById('cancelOrderModal'));
        document.getElementById('cancelOrderDetails').innerHTML = `
            <div class="alert alert-warning">
                <strong>Commande:</strong> ${orderReference}<br>
                <strong>Fournisseur:</strong> ${supplierName}
            </div>
        `;

        // Set up confirmation handler
        const confirmBtn = document.getElementById('confirmCancelBtn');
        confirmBtn.onclick = () => {
            modal.hide();
            this.performCancelOrder(orderId);
        };

        modal.show();
    },

    performCancelOrder: function(orderId) {
        fetch(`/inventory/stock-replenishment/orders/${orderId}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': this.getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showSuccess('Commande annulée avec succès');
                this.removeOrderCard(orderId);
                this.updateStats();
            } else {
                this.showError(data.error || 'Erreur inconnue');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            this.showError('Erreur de connexion');
        });
    },

    // Fonctions utilitaires
    showLoading: function(orderId) {
        const card = document.querySelector(`[data-order-id="${orderId}"]`);
        if (card) {
            card.style.opacity = '0.6';
            card.style.pointerEvents = 'none';
        }
    },

    hideLoading: function(orderId) {
        const card = document.querySelector(`[data-order-id="${orderId}"]`);
        if (card) {
            card.style.opacity = '1';
            card.style.pointerEvents = 'auto';
        }
    },

    removeOrderCard: function(orderId) {
        const card = document.querySelector(`[data-order-id="${orderId}"]`);
        if (card) {
            card.style.transition = 'all 0.3s ease';
            card.style.transform = 'scale(0.8)';
            card.style.opacity = '0';
            setTimeout(() => {
                card.remove();
            }, 300);
        }
    },

    updateStats: function() {
        // Mettre à jour les statistiques en temps réel
        const visibleCards = document.querySelectorAll('.order-card:not([style*="display: none"])');
        const statCards = document.querySelectorAll('.stat-card h3');
        
        if (statCards.length > 0) {
            statCards[0].textContent = visibleCards.length;
        }
    },

    showSuccess: function(message) {
        this.showToast(message, 'success');
    },

    showError: function(message) {
        this.showToast(message, 'error');
    },

    showToast: function(message, type) {
        // Créer un toast Bootstrap ou une alerte simple
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
        
        const toast = document.createElement('div');
        toast.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="${icon}"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Auto-remove après 5 secondes
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    },

    initKeyboardShortcuts: function() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+F pour focus sur la recherche
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }

            // Escape pour effacer les filtres
            if (e.key === 'Escape') {
                this.clearFilters();
            }
        });
    },

    initTooltips: function() {
        // Initialiser les tooltips Bootstrap si disponible
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    },

    getCSRFToken: function() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.content : '';
    },

    initAutoRefresh: function() {
        // Actualisation automatique toutes les 5 minutes
        setInterval(() => {
            this.refreshOrderStatus();
        }, 300000);
    },

    refreshOrderStatus: function() {
        // Vérifier s'il y a des mises à jour de statut
        fetch('/inventory/api/pending-orders/status-check')
            .then(response => response.json())
            .then(data => {
                if (data.updates_available) {
                    // Afficher une notification discrète
                    this.showUpdateNotification();
                }
            })
            .catch(error => {
                console.error('Erreur lors de la vérification des mises à jour:', error);
            });
    },

    showUpdateNotification: function() {
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
        notification.style.cssText = 'bottom: 20px; right: 20px; z-index: 9999;';
        notification.innerHTML = `
            <i class="fas fa-info-circle"></i> 
            Des mises à jour sont disponibles. 
            <button class="btn btn-sm btn-outline-primary ms-2" onclick="location.reload()">
                Actualiser
            </button>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
    }
};

// Fonctions globales pour les événements onclick
function applyFilters() {
    PendingOrders.applyFilters();
}

function clearFilters() {
    PendingOrders.clearFilters();
}

function markAsReceived(orderId) {
    PendingOrders.markAsReceived(orderId);
}

function partialReceive(orderId) {
    PendingOrders.partialReceive(orderId);
}

function cancelOrder(orderId) {
    PendingOrders.cancelOrder(orderId);
}

function editOrder(orderId) {
    window.location.href = `/inventory/stock-replenishment/orders/${orderId}/edit`;
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    PendingOrders.init();
});
