{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('admin.owner_users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <!-- Edit User Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Modifier l'utilisateur</h6>
        </div>
        <div class="card-body">
            <form action="{{ url_for('admin.edit_owner_user', id=user.id) }}" method="post">
                {{ form.csrf_token }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Nom d'utilisateur</label>
                            <input type="text" name="username" class="form-control" value="{{ user.username }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" name="email" class="form-control" value="{{ user.email }}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Nouveau mot de passe</label>
                            <input type="password" name="password" class="form-control" placeholder="Laisser vide pour ne pas modifier">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Rôle</label>
                            <select name="role" class="form-select" required>
                                <option value="{{ roles.ADMIN.value }}" {% if user.role == roles.ADMIN %}selected{% endif %}>Administrateur</option>
                                <option value="{{ roles.MANAGER.value }}" {% if user.role == roles.MANAGER %}selected{% endif %}>Manager</option>
                                <option value="{{ roles.CASHIER.value }}" {% if user.role == roles.CASHIER %}selected{% endif %}>Caissier</option>
                                <option value="{{ roles.KITCHEN.value }}" {% if user.role == roles.KITCHEN %}selected{% endif %}>Cuisine</option>
                                <option value="{{ roles.WAITER.value }}" {% if user.role == roles.WAITER %}selected{% endif %}>Serveur</option>
                                <option value="{{ roles.EMPLOYEE.value }}" {% if user.role == roles.EMPLOYEE %}selected{% endif %}>Employé</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" class="form-check-input" id="is_active" {% if user.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">Compte actif</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Informations complémentaires</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Date de création :</strong> {{ user.created_at | datetime }}</p>
                    <p><strong>Dernière connexion :</strong> 
                        {% if user.last_login %}
                            {{ user.last_login | datetime }}
                        {% else %}
                            Jamais connecté
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 