from flask import Flask
from .extensions import db, migrate, login, csrf, redis_client, socketio
from app.modules.auth.models import Anonymous
# Import all models for Flask-Migrate discovery
from app import models
# Register custom filters
from app.utils.filters import format_currency_filter, datetime_filter, date_filter, time_filter
from app.modules.ai_support.gemini_service import gemini_service

def create_app(config_class=None):
    app = Flask(__name__)
    if config_class is not None:
        app.config.from_object(config_class)
    
    # Configuration pour traiter les URLs avec/sans slash de la même façon
    app.url_map.strict_slashes = False
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    login.init_app(app)
    login.anonymous_user = Anonymous
    csrf.init_app(app)
    redis_client.init_app(app)
    socketio.init_app(app, cors_allowed_origins="*")

    # Register context processors
    from app.modules.cash_register.context_processors import inject_cash_register_forms
    app.context_processor(inject_cash_register_forms)
    
    # Configure login
    login.login_view = 'auth.login'
    login.login_message = 'Veuillez vous connecter pour accéder à cette page.'
    login.login_message_category = 'info'
    
    # Register custom filters
    app.jinja_env.filters['format_currency'] = format_currency_filter
    app.jinja_env.filters['datetime'] = datetime_filter
    app.jinja_env.filters['date'] = date_filter
    app.jinja_env.filters['time'] = time_filter

    # Register blueprints
    register_blueprints(app)

    # Injecte la fonction getConfidenceClass dans le contexte Jinja
    try:
        from app.modules.ai_support import init_app as ai_support_init_app
        ai_support_init_app(app)
    except Exception as e:
        app.logger.warning(f"Impossible d'injecter getConfidenceClass: {e}")

    from app import cli
    cli.init_app(app)  # Initialiser les commandes CLI

    gemini_service.init_app(app)  # Initialisation explicite de Gemini

    return app

def register_blueprints(app):
    """Register all blueprints"""
    from app.modules.auth import bp as auth_bp
    from app.modules.admin import bp as admin_bp
    from app.modules.main import bp as main_bp
    from app.modules.pos import bp as pos_bp
    from app.modules.inventory import bp as inventory_bp
    from app.modules.reports import bp as reports_bp
    from app.modules.settings import bp as settings_bp
    from app.modules.customers import bp as customers_bp
    from app.modules.promotions import bp as promotions_bp
    from app.modules.tables import bp as tables_bp
    from app.modules.rooms import bp as rooms_bp
    from app.modules.expenses import bp as expenses_bp
    from app.modules.cash_register import bp as cash_register_bp
    from app.modules.advanced import bp as advanced_bp
    from app.modules.online_ordering_sites import bp as online_ordering_bp
    from app.modules.notifications import bp as notifications_bp
    from app.modules.ai_support import bp as ai_support_bp
    from app.modules.employees import bp as employees_bp

    # Enregistrement des blueprints
    # Enregistrer le blueprint online_ordering EN PREMIER pour gérer les sous-domaines
    app.register_blueprint(online_ordering_bp)
    app.register_blueprint(main_bp, url_prefix='/')
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(promotions_bp, url_prefix='/promotions')
    app.register_blueprint(tables_bp, url_prefix='/tables')
    app.register_blueprint(rooms_bp, url_prefix='/rooms')
    app.register_blueprint(expenses_bp, url_prefix='/expenses')
    app.register_blueprint(cash_register_bp, url_prefix='/cash-register')
    app.register_blueprint(advanced_bp, url_prefix='/advanced')
    app.register_blueprint(notifications_bp, url_prefix='/notifications')
    app.register_blueprint(ai_support_bp, url_prefix='/support')
    app.register_blueprint(employees_bp, url_prefix='/employees')

    # Initialiser le service Gemini AI
    try:
        from app.modules.ai_support.gemini_service import gemini_service
        gemini_service.init_app(app)
    except Exception as e:
        app.logger.warning(f"Impossible d'initialiser le service Gemini: {e}")

    # Ajouter des filtres Jinja2 personnalisés
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """Convertit les retours à la ligne en balises <br>"""
        if text is None:
            return ''
        from markupsafe import Markup
        return Markup(str(text).replace('\n', '<br>\n'))

    @app.template_filter('truncate_words')
    def truncate_words_filter(text, length=50):
        """Tronque le texte à un nombre de mots donné"""
        if text is None:
            return ''
        words = str(text).split()
        if len(words) <= length:
            return text
        return ' '.join(words[:length]) + '...'

    # Middleware global pour gérer les sous-domaines
    @app.before_request
    def handle_subdomain_routing():
        """Middleware global pour gérer le routage par sous-domaine"""
        from flask import request, session, redirect, url_for

        # Ne traiter que les requêtes avec sous-domaines lvh.me
        if 'lvh.me' in request.host:
            subdomain = request.host.split('.')[0]

            # Stocker le sous-domaine dans la session
            session['current_subdomain'] = subdomain

            # Vérifier si c'est le site all_businesses
            if subdomain == 'all_businesses':
                session['site_type'] = 'all_businesses'
            else:
                # Vérifier si le site existe
                from app.modules.online_ordering_sites.models import OnlineOrderingSite
                site = OnlineOrderingSite.query.filter_by(
                    subdomain=subdomain,
                    is_active=True
                ).first()

                if site:
                    session['current_site_id'] = site.id
                    session['site_type'] = 'restaurant'

    # Route de test directe dans l'app principale
    @app.route('/test_direct')
    def test_direct():
        from flask import jsonify
        return jsonify({'status': 'success', 'message': 'Route directe fonctionne'})

    # Enregistrer les commandes CLI
    from . import cli_commands
    cli_commands.init_app(app)

    return app