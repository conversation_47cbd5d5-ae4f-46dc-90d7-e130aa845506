{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shopping-cart"></i>
            Commandes fournisseurs
        </h1>
        <div>
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#importModal">
                <i class="fas fa-file-import"></i>
                Importer
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#orderModal">
                <i class="fas fa-plus"></i>
                Nouvelle commande
            </button>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <!-- Pending Orders -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                En attente
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ pending_orders_count }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ "%.2f"|format(pending_orders_total) }} €
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmed Orders -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Confirmées
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ confirmed_orders_count }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ "%.2f"|format(confirmed_orders_total) }} €
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivered Orders -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Livrées (30j)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ delivered_orders_count }}
                            </div>
                            <div class="text-xs text-muted mt-2">
                                {{ "%.2f"|format(delivered_orders_total) }} €
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Lead Time -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Délai moyen
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.1f"|format(average_lead_time) }}j
                            </div>
                            <div class="text-xs text-muted mt-2">
                                sur les 30 derniers jours
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-end">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Rechercher</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" name="search" class="form-control" placeholder="Référence, fournisseur..." value="{{ request.args.get('search', '') }}">
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">Fournisseur</label>
                    <select name="supplier" class="form-select">
                        <option value="">Tous</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if request.args.get('supplier')|int == supplier.id %}selected{% endif %}>
                            {{ supplier.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">Statut</label>
                    <select name="status" class="form-select">
                        <option value="">Tous</option>
                        <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>En attente</option>
                        <option value="confirmed" {% if request.args.get('status') == 'confirmed' %}selected{% endif %}>Confirmée</option>
                        <option value="delivered" {% if request.args.get('status') == 'delivered' %}selected{% endif %}>Livrée</option>
                        <option value="cancelled" {% if request.args.get('status') == 'cancelled' %}selected{% endif %}>Annulée</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">Période</label>
                    <select name="period" class="form-select">
                        <option value="7" {% if request.args.get('period') == '7' %}selected{% endif %}>7 derniers jours</option>
                        <option value="30" {% if request.args.get('period') == '30' %}selected{% endif %}>30 derniers jours</option>
                        <option value="90" {% if request.args.get('period') == '90' %}selected{% endif %}>90 derniers jours</option>
                        <option value="all" {% if request.args.get('period') == 'all' %}selected{% endif %}>Toutes</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">Trier par</label>
                    <select name="sort" class="form-select">
                        <option value="date" {% if request.args.get('sort') == 'date' %}selected{% endif %}>Date</option>
                        <option value="reference" {% if request.args.get('sort') == 'reference' %}selected{% endif %}>Référence</option>
                        <option value="supplier" {% if request.args.get('sort') == 'supplier' %}selected{% endif %}>Fournisseur</option>
                        <option value="total" {% if request.args.get('sort') == 'total' %}selected{% endif %}>Montant</option>
                    </select>
                </div>
                <div class="col-md-1 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Fournisseur</th>
                            <th>Articles</th>
                            <th class="text-end">Montant</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr>
                            <td>
                                <a href="{{ url_for('orders.view', order_id=order.id) }}" class="text-primary">
                                    {{ order.reference }}
                                </a>
                            </td>
                            <td>{{ order.created_at | date }}</td>
                            <td>
                                <a href="{{ url_for('suppliers.view', supplier_id=order.supplier.id) }}">
                                    {{ order.supplier.name }}
                                </a>
                            </td>
                            <td>
                                {{ order.items|length }} article(s)
                                <button type="button" 
                                        class="btn btn-link btn-sm p-0 ms-1" 
                                        data-bs-toggle="popover" 
                                        data-bs-trigger="focus"
                                        data-bs-html="true"
                                        data-bs-content="{% for item in order.items %}{{ item.name }}: {{ item.quantity }} {{ item.unit }}<br>{% endfor %}">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </td>
                            <td class="text-end">{{ "%.2f"|format(order.total) }} €</td>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning">En attente</span>
                                {% elif order.status == 'confirmed' %}
                                <span class="badge bg-info">Confirmée</span>
                                {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">Livrée</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">Annulée</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('orders.view', order_id=order.id) }}" class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if order.status == 'pending' %}
                                    <button type="button" class="btn btn-outline-primary"
                                            data-bs-toggle="modal"
                                            data-bs-target="#updateStatusModal"
                                            data-order-id="{{ order.id }}"
                                            data-order-reference="{{ order.reference }}">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-outline-primary"
                                            data-bs-toggle="modal"
                                            data-bs-target="#duplicateModal"
                                            data-order-id="{{ order.id }}"
                                            data-order-reference="{{ order.reference }}"
                                            data-supplier-id="{{ order.supplier.id }}"
                                            data-supplier-name="{{ order.supplier.name }}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center text-muted">
                                Aucune commande
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item {% if not orders.has_prev %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('orders.index', page=orders.prev_num, **request.args) }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% for page in orders.iter_pages() %}
                        {% if page %}
                        <li class="page-item {% if page == orders.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('orders.index', page=page, **request.args) }}">
                                {{ page }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    <li class="page-item {% if not orders.has_next %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('orders.index', page=orders.next_num, **request.args) }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- New Order Modal -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nouvelle commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="orderForm" method="POST" action="{{ url_for('orders.create') }}">
                    <div class="mb-3">
                        <label class="form-label">Fournisseur</label>
                        <select name="supplier_id" class="form-select" required>
                            <option value="">Sélectionner un fournisseur</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Articles</label>
                        <div id="orderItems">
                            <div class="order-item mb-2">
                                <div class="input-group">
                                    <select name="items[]" class="form-select" required>
                                        <option value="">Sélectionner un article</option>
                                        {% for item in items %}
                                        <option value="{{ item.id }}" data-unit="{{ item.unit }}">{{ item.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <input type="number" name="quantities[]" class="form-control" placeholder="Qté" min="0" step="0.1" required>
                                    <span class="input-group-text item-unit">-</span>
                                    <button type="button" class="btn btn-outline-danger remove-item">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="addItem">
                            <i class="fas fa-plus"></i>
                            Ajouter un article
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="orderForm" class="btn btn-primary">
                    <i class="fas fa-shopping-cart"></i>
                    Commander
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mettre à jour le statut</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm" method="POST">
                    <p>Commande: <strong id="update-order-reference"></strong></p>
                    <div class="mb-3">
                        <label class="form-label">Nouveau statut</label>
                        <select name="status" class="form-select" required>
                            <option value="confirmed">Confirmée</option>
                            <option value="delivered">Livrée</option>
                            <option value="cancelled">Annulée</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="updateStatusForm" class="btn btn-primary">
                    <i class="fas fa-check"></i>
                    Mettre à jour
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Duplicate Order Modal -->
<div class="modal fade" id="duplicateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Dupliquer la commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="duplicateForm" method="POST">
                    <p>Commande: <strong id="duplicate-order-reference"></strong></p>
                    <p>Fournisseur: <strong id="duplicate-supplier-name"></strong></p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Une nouvelle commande sera créée avec les mêmes articles et quantités.
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="duplicateForm" class="btn btn-primary">
                    <i class="fas fa-copy"></i>
                    Dupliquer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importer des commandes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" method="POST" action="{{ url_for('orders.import_orders') }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">Fichier CSV/Excel</label>
                        <input type="file" name="file" class="form-control" accept=".csv,.xlsx" required>
                        <small class="text-muted">
                            <a href="{{ url_for('orders.download_template') }}">
                                <i class="fas fa-download"></i>
                                Télécharger le modèle
                            </a>
                        </small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="importForm" class="btn btn-primary">
                    <i class="fas fa-file-import"></i>
                    Importer
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Quick Order Modal
    const orderItems = document.getElementById('orderItems');
    const addItemBtn = document.getElementById('addItem');
    const itemTemplate = orderItems.querySelector('.order-item').cloneNode(true);

    function updateItemUnit(select) {
        const unit = select.options[select.selectedIndex].dataset.unit;
        select.closest('.order-item').querySelector('.item-unit').textContent = unit || '-';
    }

    function setupItemListeners(item) {
        const select = item.querySelector('select');
        const removeBtn = item.querySelector('.remove-item');

        select.addEventListener('change', () => updateItemUnit(select));
        removeBtn.addEventListener('click', () => {
            if (orderItems.children.length > 1) {
                item.remove();
            }
        });
    }

    addItemBtn.addEventListener('click', () => {
        const newItem = itemTemplate.cloneNode(true);
        setupItemListeners(newItem);
        orderItems.appendChild(newItem);
    });

    // Setup listeners for initial item
    setupItemListeners(orderItems.querySelector('.order-item'));

    // Update Status Modal
    const updateStatusModal = document.getElementById('updateStatusModal');
    updateStatusModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const orderId = button.dataset.orderId;
        const orderReference = button.dataset.orderReference;
        
        document.getElementById('update-order-reference').textContent = orderReference;
        document.getElementById('updateStatusForm').action = `/orders/${orderId}/status`;
    });

    // Duplicate Order Modal
    const duplicateModal = document.getElementById('duplicateModal');
    duplicateModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const orderId = button.dataset.orderId;
        const orderReference = button.dataset.orderReference;
        const supplierName = button.dataset.supplierName;
        
        document.getElementById('duplicate-order-reference').textContent = orderReference;
        document.getElementById('duplicate-supplier-name').textContent = supplierName;
        document.getElementById('duplicateForm').action = `/orders/${orderId}/duplicate`;
    });
});
</script>
{% endblock %}
{% endblock %} 