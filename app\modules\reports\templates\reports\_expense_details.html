<!-- Expense Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <h6 class="font-weight-bold">Informations générales</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Date</th>
                <td>{{ expense.date | date }}</td>
            </tr>
            <tr>
                <th>Description</th>
                <td>{{ expense.description }}</td>
            </tr>
            <tr>
                <th>Catégorie</th>
                <td>
                    <span class="badge" style="background-color: {{ expense.category.color }}">
                        {{ expense.category.name }}
                    </span>
                </td>
            </tr>
            <tr>
                <th>Référence</th>
                <td>{{ expense.reference or '-' }}</td>
            </tr>
            <tr>
                <th>Mode de paiement</th>
                <td>
                    {% if expense.payment_method == 'cash' %}
                    <i class="fas fa-money-bill text-success"></i> Espèces
                    {% elif expense.payment_method == 'card' %}
                    <i class="fas fa-credit-card text-primary"></i> Carte bancaire
                    {% elif expense.payment_method == 'check' %}
                    <i class="fas fa-money-check text-info"></i> Chèque
                    {% elif expense.payment_method == 'transfer' %}
                    <i class="fas fa-exchange-alt text-warning"></i> Virement
                    {% else %}
                    <i class="fas fa-question-circle text-muted"></i> Autre
                    {% endif %}
                </td>
            </tr>
            {% if expense.is_recurring %}
            <tr>
                <th>Récurrence</th>
                <td>
                    {% if expense.recurring_interval == 'monthly' %}
                    Mensuel (J{{ expense.recurring_day }})
                    {% elif expense.recurring_interval == 'quarterly' %}
                    Trimestriel (J{{ expense.recurring_day }})
                    {% else %}
                    Annuel (J{{ expense.recurring_day }})
                    {% endif %}
                </td>
            </tr>
            {% endif %}
        </table>
    </div>
    <div class="col-md-6">
        <h6 class="font-weight-bold">Montant</h6>
        <table class="table table-sm">
            <tr class="fw-bold">
                <th style="width: 150px">Total</th>
                <td>{{ "%.2f"|format(expense.amount) }} €</td>
            </tr>
        </table>
    </div>
</div>

<!-- Receipt -->
{% if expense.image_path %}
<h6 class="font-weight-bold mb-3">Justificatif</h6>
<div class="text-center">
    <img src="{{ url_for('static', filename=expense.image_path) }}" 
         class="img-fluid mb-3" 
         alt="Justificatif"
         style="max-height: 400px;">
    <div>
        <a href="{{ url_for('static', filename=expense.image_path) }}" 
           class="btn btn-primary"
           target="_blank">
            <i class="fas fa-external-link-alt"></i>
            Voir en plein écran
        </a>
    </div>
</div>
{% endif %}

<!-- Actions -->
<div class="mt-4 text-end">
    <a href="{{ url_for('expenses.edit', id=expense.id) }}" class="btn btn-primary">
        <i class="fas fa-edit"></i>
        Modifier
    </a>
</div> 