{% extends "ai_support/base.html" %}

{% block support_content %}
<div class="row">
    <div class="col-12">
        <h2>Chat en direct avec l'IA</h2>
        <p class="text-muted">Posez vos questions à notre assistant IA. Il est disponible 24h/24 pour vous aider.</p>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="chat-container">
            <!-- Messages -->
            <div class="chat-messages" id="chatMessages">
                {% if messages %}
                    {% for message in messages %}
                    <div class="message {{ message.sender_type.value }}" data-message-id="{{ message.id }}">
                        <div class="message-content">{{ message.content }}</div>
                        <div class="message-time">{{ message.created_at | time }}</div>
                        {% if message.sender_type.value == 'ai' and message.ai_confidence %}
                        <div class="ai-confidence {{ getConfidenceClass(message.ai_confidence) }}">
                            Confiance: {{ (message.ai_confidence * 100)|round }}%
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                <div class="message system">
                    <div class="message-content">
                        👋 Bonjour ! Je suis votre assistant IA. Comment puis-je vous aider aujourd'hui ?
                    </div>
                </div>
                {% endif %}
            </div>
            
            <!-- Zone de saisie -->
            <div class="chat-input">
                <form id="chatForm" class="d-flex">
                    <input type="hidden" id="conversationId" value="{{ conversation.id }}">
                    <input type="text" 
                           id="messageInput" 
                           class="form-control me-2" 
                           placeholder="Tapez votre message..." 
                           autocomplete="off"
                           maxlength="1000">
                    <button type="submit" class="btn btn-primary" id="sendButton">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
                
                <!-- Indicateur de frappe -->
                <div id="typingIndicator" class="mt-2 text-muted" style="display: none;">
                    <small><i class="fas fa-circle-notch fa-spin"></i> L'IA réfléchit...</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="row mt-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button class="btn btn-outline-secondary" onclick="clearChat()">
                    <i class="fas fa-trash"></i> Nouvelle conversation
                </button>
                <button class="btn btn-outline-info" onclick="exportChat()">
                    <i class="fas fa-download"></i> Exporter
                </button>
            </div>
            <div>
                <button class="btn btn-outline-warning" onclick="escalateToHuman()">
                    <i class="fas fa-user"></i> Parler à un agent
                </button>
                {% if current_user.is_admin %}
                <button class="btn btn-outline-info" onclick="debugChat()">
                    <i class="fas fa-bug"></i> Debug
                </button>
                {% endif %}
                <a href="{{ url_for('ai_support.create_ticket') }}" class="btn btn-outline-primary">
                    <i class="fas fa-ticket-alt"></i> Créer un ticket
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Suggestions rapides -->
<div class="row mt-4">
    <div class="col-12">
        <h5>Suggestions rapides</h5>
        <div class="d-flex flex-wrap gap-2">
            <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickMessage('Comment utiliser le système POS ?')">
                Comment utiliser le système POS ?
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickMessage('J\'ai un problème avec ma caisse enregistreuse')">
                Problème avec la caisse
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickMessage('Comment gérer l\'inventaire ?')">
                Gestion de l'inventaire
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickMessage('Comment générer des rapports ?')">
                Génération de rapports
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="sendQuickMessage('Aide pour la facturation')">
                Aide facturation
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {{ super() }}
{% endblock %}

{% block extra_js %}
{{ super() }}
<script>
let isWaitingForResponse = false;

// Initialisation
$(document).ready(function() {
    scrollToBottom();
    $('#messageInput').focus();
    
    // Auto-refresh des messages
    startAutoRefresh(3000);
});

// Gestion du formulaire de chat
$('#chatForm').on('submit', function(e) {
    e.preventDefault();
    
    const message = $('#messageInput').val().trim();
    if (!message || isWaitingForResponse) return;
    
    sendMessage(message);
});

// Fonction pour envoyer un message
function sendMessage(message) {
    if (isWaitingForResponse) return;
    
    isWaitingForResponse = true;
    $('#sendButton').prop('disabled', true);
    $('#messageInput').val('').prop('disabled', true);
    $('#typingIndicator').show();
    
    // Ajouter le message utilisateur immédiatement
    addMessageToChat(message, 'user', new Date().toISOString());
    
    // Préparer les données
    const requestData = {
        message: message
    };

    // Ajouter l'ID de conversation s'il existe
    const conversationId = $('#conversationId').val();
    if (conversationId && conversationId !== '') {
        requestData.conversation_id = parseInt(conversationId, 10);
    }

    // Récupère le token CSRF depuis le meta tag
    const csrfToken = $('meta[name="csrf-token"]').attr('content');

    console.log('Envoi des données:', requestData);

    // Envoyer à l'API IA (production)
    $.ajax({
        url: '{{ url_for("ai_support.api_send_message") }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        headers: {
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            console.log('Réponse reçue:', response);

            if (response.success) {
                // Mettre à jour l'ID de conversation si nécessaire
                if (response.conversation_id && $('#conversationId').val() !== response.conversation_id) {
                    $('#conversationId').val(response.conversation_id);
                    console.log('ID de conversation mis à jour:', response.conversation_id);
                }

                // Ajouter la réponse de l'IA
                addMessageToChat(
                    response.ai_message.content,
                    'ai',
                    response.ai_message.created_at,
                    response.ai_message.confidence,
                    response.help_articles // <--- Ajout des articles d'aide
                );
            } else {
                showError('Erreur lors de l\'envoi du message: ' + (response.error || 'Erreur inconnue'));
            }
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Erreur de connexion';
            showError(error);
            
            // Ajouter un message d'erreur
            addMessageToChat(
                'Désolé, je rencontre un problème technique. Veuillez réessayer ou créer un ticket de support.',
                'system',
                new Date().toISOString()
            );
        },
        complete: function() {
            isWaitingForResponse = false;
            $('#sendButton').prop('disabled', false);
            $('#messageInput').prop('disabled', false);
            $('#typingIndicator').hide();
            $('#messageInput').focus();
        }
    });
}

// Fonction pour ajouter un message au chat
function addMessageToChat(content, senderType, timestamp, confidence = null, helpArticles = null) {
    const time = new Date(timestamp).toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    let confidenceHtml = '';
    if (senderType === 'ai' && confidence) {
        const confidenceClass = getConfidenceClass(confidence);
        const confidenceText = getConfidenceText(confidence);
        confidenceHtml = `<div class="ai-confidence ${confidenceClass}">${confidenceText}</div>`;
    }
    let helpArticlesHtml = '';
    if (senderType === 'ai' && helpArticles && helpArticles.length > 0) {
        helpArticlesHtml = `<div class="help-articles"><hr><strong>Articles utiles :</strong><ul>`;
        for (const article of helpArticles) {
            helpArticlesHtml += `<li><a href="${article.url}" target="_blank">${article.title}</a></li>`;
        }
        helpArticlesHtml += '</ul></div>';
    }
    const messageHtml = `
        <div class="message ${senderType}">
            <div class="message-content">${content}</div>
            <div class="message-time">${time}</div>
            ${confidenceHtml}
            ${helpArticlesHtml}
        </div>
    `;
    $('#chatMessages').append(messageHtml);
    scrollToBottom();
}

// Fonction pour faire défiler vers le bas
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Fonction pour envoyer un message rapide
function sendQuickMessage(message) {
    $('#messageInput').val(message);
    sendMessage(message);
}

// Fonction pour vider le chat
function clearChat() {
    if (confirm('Êtes-vous sûr de vouloir commencer une nouvelle conversation ? L\'historique actuel sera perdu.')) {
        window.location.reload();
    }
}

// Fonction pour exporter le chat
function exportChat() {
    const messages = [];
    $('.message').each(function() {
        const $msg = $(this);
        const senderType = $msg.hasClass('user') ? 'Vous' : 
                          $msg.hasClass('ai') ? 'IA' : 'Système';
        const content = $msg.find('.message-content').text();
        const time = $msg.find('.message-time').text();
        
        messages.push(`[${time}] ${senderType}: ${content}`);
    });
    
    const chatContent = messages.join('\n\n');
    const blob = new Blob([chatContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-support-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Fonction pour escalader vers un agent humain
function escalateToHuman() {
    const reason = prompt('Pourquoi souhaitez-vous parler à un agent humain ?');
    if (!reason) return;
    
    // Créer un ticket automatiquement
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ url_for("ai_support.create_ticket") }}';
    
    const titleInput = document.createElement('input');
    titleInput.type = 'hidden';
    titleInput.name = 'title';
    titleInput.value = 'Escalade depuis le chat - Demande d\'agent humain';
    
    const descInput = document.createElement('input');
    descInput.type = 'hidden';
    descInput.name = 'description';
    descInput.value = `Escalade depuis le chat en direct.\n\nRaison: ${reason}\n\nHistorique de la conversation:\n${getChatHistory()}`;
    
    const categoryInput = document.createElement('input');
    categoryInput.type = 'hidden';
    categoryInput.name = 'category';
    categoryInput.value = 'general';
    
    const priorityInput = document.createElement('input');
    priorityInput.type = 'hidden';
    priorityInput.name = 'priority';
    priorityInput.value = 'high';
    
    form.appendChild(titleInput);
    form.appendChild(descInput);
    form.appendChild(categoryInput);
    form.appendChild(priorityInput);
    
    document.body.appendChild(form);
    form.submit();
}

// Fonction pour récupérer l'historique du chat
function getChatHistory() {
    const messages = [];
    $('.message').each(function() {
        const $msg = $(this);
        const senderType = $msg.hasClass('user') ? 'Utilisateur' : 
                          $msg.hasClass('ai') ? 'IA' : 'Système';
        const content = $msg.find('.message-content').text();
        const time = $msg.find('.message-time').text();
        
        messages.push(`[${time}] ${senderType}: ${content}`);
    });
    
    return messages.join('\n');
}

// Fonction pour rafraîchir les messages (appelée par auto-refresh)
function refreshChatMessages() {
    // Implémentation simple - dans une vraie application, 
    // on utiliserait WebSocket ou Server-Sent Events
    const conversationId = $('#conversationId').val();
    
    $.ajax({
        url: `{{ url_for("ai_support.api_chat_history", conversation_id=0) }}`.replace('0', conversationId),
        method: 'GET',
        success: function(response) {
            // Comparer avec les messages actuels et ajouter les nouveaux
            const currentMessages = $('.message').length;
            if (response.messages.length > currentMessages) {
                // Il y a de nouveaux messages
                location.reload(); // Solution simple pour le moment
            }
        },
        error: function() {
            // Ignorer les erreurs de rafraîchissement
        }
    });
}

// Gestion des touches
$('#messageInput').on('keypress', function(e) {
    if (e.which === 13 && !e.shiftKey) { // Entrée sans Shift
        e.preventDefault();
        $('#chatForm').submit();
    }
});

// Fonction de debug pour les administrateurs
function debugChat() {
    fetch('/support/api/debug-chat')
        .then(response => response.json())
        .then(data => {
            let debugInfo = `🔍 **Diagnostic du Chat AI**\n\n`;
            debugInfo += `**Environnement:**\n`;
            debugInfo += `- Clé API configurée: ${data.environment.GEMINI_API_KEY_SET ? '✅' : '❌'}\n`;
            debugInfo += `- Format de clé: ${data.environment.GEMINI_API_KEY_FORMAT}\n`;
            debugInfo += `- Support AI activé: ${data.environment.AI_SUPPORT_ENABLED}\n\n`;

            debugInfo += `**Service:**\n`;
            debugInfo += `- Gemini configuré: ${data.service_status.gemini_configured ? '✅' : '❌'}\n`;
            debugInfo += `- Gemini disponible: ${data.service_status.gemini_available ? '✅' : '❌'}\n`;
            debugInfo += `- Modèle: ${data.service_status.model_name}\n\n`;

            if (data.connection_test) {
                debugInfo += `**Test de connexion:**\n`;
                debugInfo += `- Succès: ${data.connection_test.success ? '✅' : '❌'}\n`;
                if (data.connection_test.error) {
                    debugInfo += `- Erreur: ${data.connection_test.error}\n`;
                    debugInfo += `- Détails: ${data.connection_test.details || 'N/A'}\n`;
                }
                if (data.connection_test.response) {
                    debugInfo += `- Réponse test: ${data.connection_test.response}\n`;
                }
            }

            // Afficher dans une modal ou alert
            alert(debugInfo);
        })
        .catch(error => {
            alert(`Erreur lors du diagnostic: ${error.message}`);
        });
}

// Fonction utilitaire pour afficher les erreurs
function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.support-content').prepend(alertHtml);
}
</script>
{% endblock %}
