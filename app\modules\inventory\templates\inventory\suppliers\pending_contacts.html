{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-clock text-warning"></i> Contacts en Attente de Suivi</h1>
        <a href="{{ url_for('inventory.suppliers') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour aux Fournisseurs
        </a>
    </div>

    {% if contacts %}
        <div class="row">
            {% for contact in contacts %}
            <div class="col-md-6 mb-4">
                <div class="card h-100 {% if contact.is_overdue %}border-danger{% endif %}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="{{ contact.contact_type_icon }} text-primary"></i>
                            <strong>{{ contact.contact_type_display }}</strong>
                            {% if contact.is_important %}
                                <i class="fas fa-star text-warning ms-2" title="Important"></i>
                            {% endif %}
                            {% if contact.is_overdue %}
                                <i class="fas fa-exclamation-triangle text-danger ms-2" title="En retard"></i>
                            {% endif %}
                        </div>
                        <small class="text-muted">{{ contact.contact_date | date }}</small>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">
                            <a href="{{ url_for('inventory.supplier_details', id=contact.supplier.id) }}" class="text-decoration-none">
                                {{ contact.supplier.name }}
                            </a>
                        </h6>
                        {% if contact.subject %}
                            <p class="card-subtitle mb-2 text-muted">{{ contact.subject }}</p>
                        {% endif %}
                        <p class="card-text">{{ contact.content|truncate(100) }}</p>
                        
                        <div class="mt-3">
                            {% if contact.follow_up_date %}
                                <small class="{% if contact.is_overdue %}text-danger{% else %}text-muted{% endif %}">
                                    <i class="fas fa-calendar-alt"></i>
                                    Suivi prévu: {{ contact.follow_up_date | datetime(\'%d/%m/%Y\', \'à %H:%M\') }}
                                    {% if contact.is_overdue %}
                                        <strong>(En retard)</strong>
                                    {% endif %}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            <a href="{{ url_for('inventory.edit_supplier_contact', supplier_id=contact.supplier.id, contact_id=contact.id) }}" 
                               class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i> Traiter
                            </a>
                            <a href="{{ url_for('inventory.supplier_contacts', supplier_id=contact.supplier.id) }}" 
                               class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye"></i> Voir Tous
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <h3 class="text-muted">Aucun contact en attente</h3>
            <p class="text-muted">Tous vos contacts fournisseurs sont à jour !</p>
            <a href="{{ url_for('inventory.suppliers') }}" class="btn btn-primary">
                <i class="fas fa-truck"></i> Voir les Fournisseurs
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
