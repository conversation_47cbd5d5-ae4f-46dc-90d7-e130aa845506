{% extends "base.html" %}

{% block title %}{{ employee.full_name }} - Détails Employé{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-user me-2"></i>{{ employee.full_name }}
                    {% if employee.status == 'ACTIVE' %}
                        <span class="badge bg-success ms-2">Actif</span>
                    {% elif employee.status == 'INACTIVE' %}
                        <span class="badge bg-secondary ms-2">Inactif</span>
                    {% elif employee.status == 'SUSPENDED' %}
                        <span class="badge bg-warning ms-2">Suspendu</span>
                    {% elif employee.status == 'TERMINATED' %}
                        <span class="badge bg-danger ms-2">Terminé</span>
                    {% endif %}
                </h1>
                <div>
                    <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                    <a href="{{ url_for('employees.edit', id=employee.id) }}" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('employees.profile', id=employee.id) }}">
                                <i class="fas fa-user-edit me-2"></i>Gérer le profil
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('employees.schedules', id=employee.id) }}">
                                <i class="fas fa-calendar me-2"></i>Plannings
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('employees.attendance', id=employee.id) }}">
                                <i class="fas fa-clock me-2"></i>Présences
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('employees.payroll', id=employee.id) }}">
                                <i class="fas fa-money-bill-wave me-2"></i>Paie
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('employees.performance', id=employee.id) }}">
                                <i class="fas fa-chart-line me-2"></i>Évaluations
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Informations principales -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Informations Générales
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center">
                                    <span class="fs-2">{{ employee.first_name[0] }}{{ employee.last_name[0] }}</span>
                                </div>
                                <h4 class="mt-2 mb-0">{{ employee.full_name }}</h4>
                                <p class="text-muted">{{ employee.position }}</p>
                            </div>
                            
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>ID Employé:</strong></td>
                                    <td>{{ employee.employee_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Département:</strong></td>
                                    <td>{{ employee.department or '-' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Type de contrat:</strong></td>
                                    <td>{{ employee.contract_type.replace('_', ' ').title() }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Date d'embauche:</strong></td>
                                    <td>{{ employee.hire_date | date }}</td>
                                </tr>
                                {% if employee.termination_date %}
                                <tr>
                                    <td><strong>Date de fin:</strong></td>
                                    <td>{{ employee.termination_date | date }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- Contact -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-address-book me-2"></i>Contact
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if employee.email %}
                            <div class="mb-2">
                                <i class="fas fa-envelope me-2 text-muted"></i>
                                <a href="mailto:{{ employee.email }}">{{ employee.email }}</a>
                            </div>
                            {% endif %}
                            {% if employee.phone %}
                            <div class="mb-2">
                                <i class="fas fa-phone me-2 text-muted"></i>
                                <a href="tel:{{ employee.phone }}">{{ employee.phone }}</a>
                            </div>
                            {% endif %}
                            {% if employee.address %}
                            <div class="mb-2">
                                <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                {{ employee.address }}
                            </div>
                            {% endif %}
                            {% if employee.date_of_birth %}
                            <div class="mb-2">
                                <i class="fas fa-birthday-cake me-2 text-muted"></i>
                                {{ employee.date_of_birth | date }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Statistiques et activité récente -->
                <div class="col-lg-8">
                    <!-- Statistiques du mois -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">{{ monthly_attendances }}</h4>
                                            <p class="mb-0">Présences ce mois</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">{{ "%.1f"|format(monthly_hours) }}h</h4>
                                            <p class="mb-0">Heures travaillées</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">
                                                {% if last_performance %}
                                                    {{ "%.1f"|format(last_performance.overall_score) }}/5
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </h4>
                                            <p class="mb-0">Dernière évaluation</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-star fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations salariales -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-money-bill-wave me-2"></i>Informations Salariales
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Salaire de base:</strong><br>
                                    <span class="h5 text-primary">{{ employee.base_salary|format_currency }}</span>
                                </div>
                                {% if employee.hourly_rate %}
                                <div class="col-md-4">
                                    <strong>Taux horaire:</strong><br>
                                    <span class="h5 text-success">{{ employee.hourly_rate|format_currency }}/h</span>
                                </div>
                                {% endif %}
                                <div class="col-md-4">
                                    <strong>Fréquence de paiement:</strong><br>
                                    <span class="text-muted">{{ employee.payment_frequency.replace('_', ' ').title() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Actions Rapides
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('employees.new_attendance', id=employee.id) }}" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-clock me-1"></i>Nouvelle Présence
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('employees.new_schedule', id=employee.id) }}" class="btn btn-outline-success w-100">
                                        <i class="fas fa-calendar-plus me-1"></i>Nouveau Planning
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('employees.new_payroll', id=employee.id) }}" class="btn btn-outline-info w-100">
                                        <i class="fas fa-money-bill me-1"></i>Nouvelle Paie
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('employees.new_performance', id=employee.id) }}" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-star me-1"></i>Évaluation
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    font-size: 24px;
    font-weight: bold;
}
</style>
{% endblock %}
