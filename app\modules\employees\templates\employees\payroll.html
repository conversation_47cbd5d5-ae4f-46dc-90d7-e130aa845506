{% extends "employees/base_hr.html" %}

{% block title %}Paie - {{ employee.full_name }}{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-money-bill-wave me-2"></i>Paie de {{ employee.full_name }}
    </h1>
    <div>
        <a href="{{ url_for('employees.new_payroll', id=employee.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i>Nouvelle Fiche de Paie
        </a>
        <a href="{{ url_for('employees.detail', id=employee.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour
        </a>
    </div>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ payrolls|length }}</h4>
                        <p class="mb-0">Fiches de Paie</p>
                    </div>
                    <i class="fas fa-file-invoice-dollar fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.0f"|format(total_net_pay or 0) }}€</h4>
                        <p class="mb-0">Total Net</p>
                    </div>
                    <i class="fas fa-hand-holding-usd fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.0f"|format(total_hours or 0) }}h</h4>
                        <p class="mb-0">Heures Totales</p>
                    </div>
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ "%.0f"|format(avg_monthly_pay or 0) }}€</h4>
                        <p class="mb-0">Moyenne Mensuelle</p>
                    </div>
                    <i class="fas fa-chart-line fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="year" class="form-label">Année</label>
                <select class="form-select" id="year" name="year">
                    {% for year in range(2020, 2030) %}
                        <option value="{{ year }}" {{ 'selected' if year == current_year }}>{{ year }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="month" class="form-label">Mois</label>
                <select class="form-select" id="month" name="month">
                    <option value="">Tous les mois</option>
                    {% for i in range(1, 13) %}
                        <option value="{{ i }}" {{ 'selected' if i == current_month }}>
                            {{ ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
                                 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'][i] }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-filter me-1"></i>Filtrer
                </button>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-outline-success" onclick="exportPayroll()">
                    <i class="fas fa-download me-1"></i>Exporter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Liste des fiches de paie -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Historique des Fiches de Paie
        </h5>
    </div>
    <div class="card-body p-0">
        {% if payrolls %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Période</th>
                        <th>Heures Normales</th>
                        <th>Heures Sup.</th>
                        <th>Salaire Brut</th>
                        <th>Déductions</th>
                        <th>Salaire Net</th>
                        <th>Date de Paie</th>
                        <th>Statut</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payroll in payrolls %}
                    <tr>
                        <td>
                            <strong>{{ payroll.pay_period_start | date }}</strong><br>
                            <small class="text-muted">au {{ payroll.pay_period_end | date }}</small>
                        </td>
                        <td>{{ "%.1f"|format(payroll.regular_hours or 0) }}h</td>
                        <td>{{ "%.1f"|format(payroll.overtime_hours or 0) }}h</td>
                        <td><strong>{{ "%.2f"|format(payroll.gross_pay or 0) }}€</strong></td>
                        <td>{{ "%.2f"|format(payroll.total_deductions or 0) }}€</td>
                        <td><strong class="text-success">{{ "%.2f"|format(payroll.net_pay or 0) }}€</strong></td>
                        <td>
                            {% if payroll.pay_date %}
                                {{ payroll.pay_date | date }}
                            {% else %}
                                <span class="text-muted">Non définie</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if payroll.is_processed %}
                                <span class="badge bg-success">Traitée</span>
                            {% else %}
                                <span class="badge bg-warning">En attente</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.edit_payroll', payroll_id=payroll.id) }}" 
                                   class="btn btn-sm btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        onclick="downloadPayslip({{ payroll.id }})" title="Télécharger">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" 
                                        onclick="markAsProcessed({{ payroll.id }})" title="Marquer comme traitée">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune fiche de paie</h5>
            <p class="text-muted">Créez la première fiche de paie pour cet employé.</p>
            <a href="{{ url_for('employees.new_payroll', id=employee.id) }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Créer une Fiche de Paie
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Graphique des salaires -->
{% if payrolls %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chart-line me-2"></i>Évolution des Salaires
        </h5>
    </div>
    <div class="card-body">
        <canvas id="salaryChart" width="400" height="100"></canvas>
    </div>
</div>
{% endif %}

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if payrolls %}
// Graphique d'évolution des salaires
const salaryCtx = document.getElementById('salaryChart').getContext('2d');
const salaryChart = new Chart(salaryCtx, {
    type: 'line',
    data: {
        labels: [
            {% for payroll in payrolls %}
            '{{ payroll.pay_period_start | date(\'%m/%Y\') }}'{{ ',' if not loop.last }}
            {% endfor %}
        ],
        datasets: [{
            label: 'Salaire Brut',
            data: [
                {% for payroll in payrolls %}
                {{ payroll.gross_pay or 0 }}{{ ',' if not loop.last }}
                {% endfor %}
            ],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.1
        }, {
            label: 'Salaire Net',
            data: [
                {% for payroll in payrolls %}
                {{ payroll.net_pay or 0 }}{{ ',' if not loop.last }}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + '€';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + context.parsed.y + '€';
                    }
                }
            }
        }
    }
});
{% endif %}

function exportPayroll() {
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;
    const url = `/employees/{{ employee.id }}/payroll/export?year=${year}&month=${month}`;
    window.open(url, '_blank');
}

function downloadPayslip(payrollId) {
    const url = `/employees/payroll/${payrollId}/download`;
    window.open(url, '_blank');
}

function markAsProcessed(payrollId) {
    if (confirm('Marquer cette fiche de paie comme traitée ?')) {
        fetch(`/employees/payroll/${payrollId}/process`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur : ' + error.message);
        });
    }
}
</script>
{% endblock %}
