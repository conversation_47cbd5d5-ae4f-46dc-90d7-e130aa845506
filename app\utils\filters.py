from app.utils.helpers import (
    format_datetime, format_currency, convert_utc_to_user_timezone, get_user_timezone
)

def datetime_filter(value, date_format=None, time_format=None, user_timezone=None):
    """Format a datetime object for display in templates with user timezone"""
    if value is None:
        return ''

    # Utiliser le fuseau horaire de l'utilisateur connecté si non spécifié
    if user_timezone is None:
        user_timezone = get_user_timezone()

    return format_datetime(value, date_format, time_format, user_timezone)

def date_filter(value, date_format=None, user_timezone=None):
    """Format a date object for display in templates with user timezone"""
    if value is None:
        return ''

    # Convertir vers le fuseau horaire utilisateur
    if hasattr(value, 'tzinfo') and value.tzinfo is not None:
        value = convert_utc_to_user_timezone(value, user_timezone)

    if date_format is None:
        date_format = '%d/%m/%Y'

    return value.strftime(date_format)

def time_filter(value, time_format=None, user_timezone=None):
    """Format a time object for display in templates with user timezone"""
    if value is None:
        return ''

    # Convertir vers le fuseau horaire utilisateur
    if hasattr(value, 'tzinfo') and value.tzinfo is not None:
        value = convert_utc_to_user_timezone(value, user_timezone)

    if time_format is None:
        time_format = '%H:%M'

    return value.strftime(time_format)

def format_currency_filter(value):
    """Format a number as currency"""
    if value is None:
        return ''
    return format_currency(value)
