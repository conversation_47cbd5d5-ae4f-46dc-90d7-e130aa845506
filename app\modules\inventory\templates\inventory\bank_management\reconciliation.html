{% extends "base.html" %}

{% block title %}Réconciliation Bancaire{% endblock %}

{% block head_scripts %}
<link rel="stylesheet" href="{{ url_for('inventory.static', filename='css/stock_replenishment.css') }}">
{% endblock %}

{% block content %}
<div class="stock-replenishment-container">
    <!-- En-tête -->
    <div class="replenishment-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center py-2">
                <h4 class="mb-0">
                    <i class="fas fa-balance-scale"></i> Réconciliation Bancaire
                </h4>
                <div class="header-actions">
                    <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Formulaire de réconciliation -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-file-alt"></i> Nouveau Rapprochement</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="reconciliationForm">
                            {{ form.hidden_tag() }}
                            
                            <!-- Compte bancaire -->
                            <div class="mb-3">
                                {{ form.bank_account_id.label(class="form-label") }}
                                {{ form.bank_account_id(class="form-select", id="bankAccountSelect") }}
                                {% if form.bank_account_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.bank_account_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Date du relevé -->
                            <div class="mb-3">
                                {{ form.statement_date.label(class="form-label") }}
                                {{ form.statement_date(class="form-control") }}
                                {% if form.statement_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.statement_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Solde du relevé -->
                            <div class="mb-3">
                                {{ form.statement_balance.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.statement_balance(class="form-control", step="0.01", id="statementBalance") }}
                                    <span class="input-group-text">€</span>
                                </div>
                                {% if form.statement_balance.errors %}
                                    <div class="text-danger">
                                        {% for error in form.statement_balance.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Informations du compte -->
                            <div class="alert alert-info" id="accountInfo" style="display: none;">
                                <h6><i class="fas fa-info-circle"></i> Informations du Compte</h6>
                                <div id="accountDetails"></div>
                            </div>

                            <!-- Analyse de l'écart -->
                            <div class="alert" id="differenceAnalysis" style="display: none;">
                                <h6><i class="fas fa-calculator"></i> Analyse de l'Écart</h6>
                                <div id="differenceDetails"></div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="4", placeholder="Notes sur la réconciliation, écarts identifiés, actions à prendre...") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Boutons -->
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('inventory.bank_management_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Enregistrer la réconciliation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Aide et historique -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-question-circle"></i> Guide de Réconciliation</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>Comment procéder ?</h6>
                            <ol class="small mb-0">
                                <li>Sélectionnez le compte bancaire</li>
                                <li>Indiquez la date de votre relevé</li>
                                <li>Saisissez le solde indiqué sur le relevé</li>
                                <li>Analysez l'écart affiché</li>
                                <li>Notez les observations</li>
                            </ol>
                        </div>

                        <div class="mt-3">
                            <h6>Causes d'écarts fréquentes</h6>
                            <ul class="small">
                                <li>Opérations en cours de traitement</li>
                                <li>Frais bancaires non saisis</li>
                                <li>Virements non comptabilisés</li>
                                <li>Erreurs de saisie</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Réconciliations récentes -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-history"></i> Réconciliations Récentes</h6>
                    </div>
                    <div class="card-body">
                        {% if recent_reconciliations %}
                            {% for recon in recent_reconciliations %}
                            <div class="border-bottom py-2">
                                <div class="d-flex justify-content-between">
                                    <strong>{{ recon.account_name }}</strong>
                                    <small class="text-muted">{{ recon.date | date }}</small>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span class="small">Solde relevé: {{ "%.2f"|format(recon.statement_balance) }} €</span>
                                    <span class="small {{ 'text-success' if recon.difference == 0 else 'text-warning' }}">
                                        Écart: {{ "%.2f"|format(recon.difference) }} €
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted small">Aucune réconciliation récente</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Opérations récentes -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-list"></i> Opérations Récentes</h6>
                    </div>
                    <div class="card-body">
                        <div id="recentOperations">
                            <p class="text-muted small">Sélectionnez un compte pour voir les opérations</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const bankAccountSelect = document.getElementById('bankAccountSelect');
    const statementBalance = document.getElementById('statementBalance');
    const accountInfo = document.getElementById('accountInfo');
    const accountDetails = document.getElementById('accountDetails');
    const differenceAnalysis = document.getElementById('differenceAnalysis');
    const differenceDetails = document.getElementById('differenceDetails');
    const recentOperations = document.getElementById('recentOperations');

    // Données des comptes
    const bankAccounts = {{ bank_accounts_json | safe }};

    function updateAccountInfo() {
        const selectedAccountId = bankAccountSelect.value;
        
        if (selectedAccountId && selectedAccountId !== '0') {
            const account = bankAccounts.find(a => a.id == selectedAccountId);
            
            if (account) {
                accountDetails.innerHTML = `
                    <div class="row">
                        <div class="col-6">
                            <strong>Compte:</strong><br>
                            ${account.name}
                        </div>
                        <div class="col-6">
                            <strong>Solde actuel:</strong><br>
                            <span class="h6 text-${account.balance >= 0 ? 'success' : 'danger'}">
                                ${account.balance.toFixed(2)} €
                            </span>
                        </div>
                    </div>
                `;
                accountInfo.style.display = 'block';
                
                // Charger les opérations récentes
                loadRecentOperations(selectedAccountId);
            }
        } else {
            accountInfo.style.display = 'none';
            recentOperations.innerHTML = '<p class="text-muted small">Sélectionnez un compte pour voir les opérations</p>';
        }
        
        updateDifferenceAnalysis();
    }

    function updateDifferenceAnalysis() {
        const selectedAccountId = bankAccountSelect.value;
        const statementBalanceValue = parseFloat(statementBalance.value) || 0;
        
        if (selectedAccountId && selectedAccountId !== '0' && statementBalanceValue !== 0) {
            const account = bankAccounts.find(a => a.id == selectedAccountId);
            
            if (account) {
                const difference = statementBalanceValue - account.balance;
                const absDifference = Math.abs(difference);
                
                let alertClass = 'alert-success';
                let icon = 'fas fa-check-circle';
                let message = 'Comptes équilibrés';
                
                if (absDifference > 0.01) {
                    alertClass = difference > 0 ? 'alert-warning' : 'alert-danger';
                    icon = 'fas fa-exclamation-triangle';
                    message = difference > 0 ? 'Solde relevé supérieur' : 'Solde relevé inférieur';
                }
                
                differenceAnalysis.className = `alert ${alertClass}`;
                differenceDetails.innerHTML = `
                    <div class="row">
                        <div class="col-4">
                            <strong>Solde système:</strong><br>
                            ${account.balance.toFixed(2)} €
                        </div>
                        <div class="col-4">
                            <strong>Solde relevé:</strong><br>
                            ${statementBalanceValue.toFixed(2)} €
                        </div>
                        <div class="col-4">
                            <strong>Écart:</strong><br>
                            <span class="text-${difference >= 0 ? 'success' : 'danger'}">
                                <i class="${icon}"></i>
                                ${difference > 0 ? '+' : ''}${difference.toFixed(2)} €
                            </span>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small><strong>Statut:</strong> ${message}</small>
                    </div>
                `;
                
                differenceAnalysis.style.display = 'block';
            }
        } else {
            differenceAnalysis.style.display = 'none';
        }
    }

    function loadRecentOperations(accountId) {
        fetch(`/inventory/api/bank-operations/${accountId}/recent`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.operations.length > 0) {
                    recentOperations.innerHTML = data.operations.map(op => `
                        <div class="border-bottom py-1">
                            <div class="d-flex justify-content-between">
                                <small><strong>${op.description}</strong></small>
                                <small class="text-muted">${op.date}</small>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">${op.type}</small>
                                <small class="text-${op.amount > 0 ? 'success' : 'danger'}">
                                    ${op.amount > 0 ? '+' : ''}${op.amount.toFixed(2)} €
                                </small>
                            </div>
                        </div>
                    `).join('');
                } else {
                    recentOperations.innerHTML = '<p class="text-muted small">Aucune opération récente</p>';
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                recentOperations.innerHTML = '<p class="text-danger small">Erreur lors du chargement</p>';
            });
    }

    // Événements
    bankAccountSelect.addEventListener('change', updateAccountInfo);
    statementBalance.addEventListener('input', updateDifferenceAnalysis);

    // Validation du formulaire
    document.getElementById('reconciliationForm').addEventListener('submit', function(e) {
        const selectedAccountId = bankAccountSelect.value;
        const statementBalanceValue = parseFloat(statementBalance.value) || 0;
        
        if (!selectedAccountId || selectedAccountId === '0') {
            e.preventDefault();
            alert('Veuillez sélectionner un compte bancaire');
            return;
        }
        
        if (statementBalanceValue === 0) {
            e.preventDefault();
            alert('Veuillez saisir le solde du relevé');
            return;
        }
        
        const account = bankAccounts.find(a => a.id == selectedAccountId);
        if (account) {
            const difference = Math.abs(statementBalanceValue - account.balance);
            if (difference > 100) {
                if (!confirm(`L'écart est important (${difference.toFixed(2)} €). Confirmer la réconciliation ?`)) {
                    e.preventDefault();
                    return;
                }
            }
        }
    });

    // Initialiser la date à aujourd'hui
    const dateInput = document.querySelector('input[name="statement_date"]');
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
</script>
{% endblock %}
