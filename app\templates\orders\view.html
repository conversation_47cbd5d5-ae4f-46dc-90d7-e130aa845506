{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-shopping-cart"></i>
                        Commande {{ order.reference }}
                    </h1>
                    <div class="text-muted">
                        {{ order.created_at | datetime(\'%d/%m/%Y\', \'à %H:%M\') }}
                        par {{ order.created_by.name }}
                    </div>
                </div>
                <div>
                    {% if order.status == 'pending' %}
                    <button type="button" class="btn btn-outline-primary" 
                            data-bs-toggle="modal" 
                            data-bs-target="#updateStatusModal"
                            data-order-id="{{ order.id }}"
                            data-order-reference="{{ order.reference }}">
                        <i class="fas fa-check"></i>
                        Mettre à jour le statut
                    </button>
                    {% endif %}
                    <button type="button" class="btn btn-outline-primary"
                            data-bs-toggle="modal"
                            data-bs-target="#duplicateModal"
                            data-order-id="{{ order.id }}"
                            data-order-reference="{{ order.reference }}"
                            data-supplier-id="{{ order.supplier.id }}"
                            data-supplier-name="{{ order.supplier.name }}">
                        <i class="fas fa-copy"></i>
                        Dupliquer
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-file-export"></i>
                            Exporter
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a href="{{ url_for('orders.export_pdf', order_id=order.id) }}" class="dropdown-item">
                                    <i class="fas fa-file-pdf"></i>
                                    PDF
                                </a>
                            </li>
                            <li>
                                <a href="{{ url_for('orders.export_excel', order_id=order.id) }}" class="dropdown-item">
                                    <i class="fas fa-file-excel"></i>
                                    Excel
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Order Items -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list"></i>
                        Articles
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Article</th>
                                    <th>Catégorie</th>
                                    <th class="text-end">Quantité</th>
                                    <th class="text-end">Prix unitaire</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order.items %}
                                <tr>
                                    <td>{{ item.name }}</td>
                                    <td>
                                        {% if item.category %}
                                        <span class="badge" style="background-color: {{ item.category.color }}">
                                            {{ item.category.name }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">
                                        {{ "%.2f"|format(item.quantity) }} {{ item.unit }}
                                    </td>
                                    <td class="text-end">{{ "%.2f"|format(item.unit_price) }} €</td>
                                    <td class="text-end">{{ "%.2f"|format(item.total) }} €</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end">
                                        <strong>Total</strong>
                                    </td>
                                    <td class="text-end">
                                        <strong>{{ "%.2f"|format(order.total) }} €</strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Status History -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history"></i>
                        Historique
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for status in order.status_history %}
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                {% if status.status == 'pending' %}
                                <i class="fas fa-clock text-warning"></i>
                                {% elif status.status == 'confirmed' %}
                                <i class="fas fa-check text-info"></i>
                                {% elif status.status == 'delivered' %}
                                <i class="fas fa-truck text-success"></i>
                                {% elif status.status == 'cancelled' %}
                                <i class="fas fa-times text-danger"></i>
                                {% endif %}
                            </div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div>
                                        {% if status.status == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                        {% elif status.status == 'confirmed' %}
                                        <span class="badge bg-info">Confirmée</span>
                                        {% elif status.status == 'delivered' %}
                                        <span class="badge bg-success">Livrée</span>
                                        {% elif status.status == 'cancelled' %}
                                        <span class="badge bg-danger">Annulée</span>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">
                                        {{ status.created_at | datetime(\'%d/%m/%Y\', \'à %H:%M\') }}
                                    </small>
                                </div>
                                {% if status.notes %}
                                <p class="mb-0">{{ status.notes }}</p>
                                {% endif %}
                                <small class="text-muted">par {{ status.created_by.name }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Supplier Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-truck"></i>
                        Fournisseur
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        {% if order.supplier.logo_path %}
                        <img src="{{ url_for('static', filename=order.supplier.logo_path) }}" 
                             class="rounded-circle me-2" 
                             alt="{{ order.supplier.name }}"
                             style="width: 32px; height: 32px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded-circle me-2 text-center" style="width: 32px; height: 32px; line-height: 32px;">
                            <i class="fas fa-building text-muted"></i>
                        </div>
                        {% endif %}
                        <div>
                            <a href="{{ url_for('suppliers.view', supplier_id=order.supplier.id) }}" class="text-primary">
                                {{ order.supplier.name }}
                            </a>
                            {% if order.supplier.company %}
                            <br>
                            <small class="text-muted">{{ order.supplier.company }}</small>
                            {% endif %}
                        </div>
                    </div>

                    {% if order.supplier.email %}
                    <div class="mb-2">
                        <a href="mailto:{{ order.supplier.email }}" class="text-primary">
                            <i class="fas fa-envelope"></i>
                            {{ order.supplier.email }}
                        </a>
                    </div>
                    {% endif %}

                    {% if order.supplier.phone %}
                    <div class="mb-2">
                        <a href="tel:{{ order.supplier.phone }}" class="text-primary">
                            <i class="fas fa-phone"></i>
                            {{ order.supplier.phone }}
                        </a>
                    </div>
                    {% endif %}

                    {% if order.supplier.address %}
                    <div class="mb-2">
                        <i class="fas fa-map-marker-alt text-muted"></i>
                        {{ order.supplier.address }}<br>
                        {{ order.supplier.postal_code }} {{ order.supplier.city }}<br>
                        {{ order.supplier.country }}
                    </div>
                    {% endif %}

                    {% if order.supplier.delivery_days %}
                    <div class="mb-2">
                        <i class="fas fa-calendar text-muted"></i>
                        Livraison:
                        {% for day in order.supplier.delivery_days %}
                        <span class="badge bg-light text-dark">{{ day }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}

                    {% if order.supplier.delivery_time %}
                    <div>
                        <i class="fas fa-clock text-muted"></i>
                        Délai: {{ order.supplier.delivery_time }} jours
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Notes Card -->
            {% if order.notes %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-sticky-note"></i>
                        Notes
                    </h6>
                </div>
                <div class="card-body">
                    {{ order.notes }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mettre à jour le statut</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm" method="POST" action="{{ url_for('orders.update_status', order_id=order.id) }}">
                    <div class="mb-3">
                        <label class="form-label">Nouveau statut</label>
                        <select name="status" class="form-select" required>
                            <option value="confirmed">Confirmée</option>
                            <option value="delivered">Livrée</option>
                            <option value="cancelled">Annulée</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="updateStatusForm" class="btn btn-primary">
                    <i class="fas fa-check"></i>
                    Mettre à jour
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Duplicate Order Modal -->
<div class="modal fade" id="duplicateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Dupliquer la commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="duplicateForm" method="POST" action="{{ url_for('orders.duplicate', order_id=order.id) }}">
                    <p>Commande: <strong>{{ order.reference }}</strong></p>
                    <p>Fournisseur: <strong>{{ order.supplier.name }}</strong></p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Une nouvelle commande sera créée avec les mêmes articles et quantités.
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="submit" form="duplicateForm" class="btn btn-primary">
                    <i class="fas fa-copy"></i>
                    Dupliquer
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 3rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    width: 2rem;
    height: 2rem;
    text-align: center;
    line-height: 2rem;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    background: #fff;
    padding: 1rem;
    border-radius: 0.35rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
{% endblock %}
{% endblock %} 