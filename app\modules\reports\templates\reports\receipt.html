<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Reçu #{{ sale.reference }}</title>
    <style>
        @media print {
            body {
                width: 80mm;
                margin: 0;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
            }
            .logo {
                max-width: 150px;
                margin-bottom: 10px;
            }
            .info {
                margin-bottom: 20px;
            }
            .info p {
                margin: 5px 0;
            }
            .items {
                width: 100%;
                border-top: 1px dashed #000;
                border-bottom: 1px dashed #000;
                margin-bottom: 20px;
                padding: 10px 0;
            }
            .item {
                margin: 5px 0;
            }
            .item-total {
                float: right;
            }
            .totals {
                margin-bottom: 20px;
            }
            .total-line {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
            }
            .grand-total {
                font-weight: bold;
                font-size: 14px;
                border-top: 1px dashed #000;
                padding-top: 5px;
            }
            .footer {
                text-align: center;
                font-size: 10px;
            }
            @page {
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ config.get('SHOP_NAME', 'Mon magasin') }}</h1>
        <p>{{ config.get('SHOP_ADDRESS', '') }}</p>
        <p>{{ config.get('SHOP_PHONE', '') }}</p>
        {% if config.get('SHOP_EMAIL') %}
        <p>{{ config.get('SHOP_EMAIL') }}</p>
        {% endif %}
    </div>

    <div class="info">
        <p>Reçu #{{ sale.reference }}</p>
        <p>Date : {{ sale.created_at | datetime }}</p>
        {% if sale.table_number %}
        <p>Table : {{ sale.table_number }}</p>
        {% endif %}
    </div>

    <div class="items">
        {% for item in sale.items.all() %}
        <div class="item">
            <span>{{ item.quantity }}x {{ item.product.name }}</span>
            <span class="item-total">{{ "%.2f"|format(item.total) }} €</span>
            <div style="clear: both;"></div>
        </div>
        {% endfor %}
    </div>

    <div class="totals">
        <div class="total-line">
            <span>Total HT</span>
            <span>{{ "%.2f"|format(sale.total_ht) }} €</span>
        </div>
        <div class="total-line">
            <span>TVA</span>
            <span>{{ "%.2f"|format(sale.total_tax) }} €</span>
        </div>
        {% if sale.discount_amount > 0 %}
        <div class="total-line">
            <span>Remise</span>
            <span>{{ "%.2f"|format(sale.discount_amount) }} €</span>
        </div>
        {% endif %}
        <div class="total-line grand-total">
            <span>Total TTC</span>
            <span>{{ "%.2f"|format(sale.total_ttc) }} €</span>
        </div>
    </div>

    <div class="footer">
        <p>Merci de votre visite !</p>
        {% if config.get('SHOP_VAT_NUMBER') %}
        <p>N° TVA : {{ config.get('SHOP_VAT_NUMBER') }}</p>
        {% endif %}
        {% if config.get('SHOP_SIRET') %}
        <p>SIRET : {{ config.get('SHOP_SIRET') }}</p>
        {% endif %}
    </div>

    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html> 